﻿using AutoPoint.Stitching.Contracts.Enums;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Reflection;
using System.Text;
using System.Threading;

namespace AutoPoint.Stitching.Common.Logger
{
    /// <summary>
    /// Quick and dirty extension methods to DMELogger that includes Job Manager audit logs.
    /// This is meant to be re-visited, as time permits.
    /// <remarks>
    /// TODO: Identify if it's possible to add a Job Manager log listener. At the time of
    /// this writing, the sticking point is the TaskWk requirement.
    /// </remarks>
    /// </summary>
    public static class DMELoggerExtension
    {

        /// <summary>
        /// Job Manager message type enumeration.
        /// <remarks>
        /// Enumeration equivalent to [RRS TFS Branch] DMERRS.Common.TaskMsgTypeEnum.
        /// </remarks>
        /// </summary>
        private enum JobManagerTaskMsgTypeEnum
        {
            Error = 0,
            Failure = 1,
            Information = 2,
            Warning = 3,
        }

        private static int? s_wordWrapLength;
        public static int WordWrapLength
        {
            get
            {
                const int DefaultLength = 100;
                if (s_wordWrapLength == null)
                {
                    s_wordWrapLength = 100;
                }
                return s_wordWrapLength.Value;
            }
        }

        /// <summary>
        /// Purpose: Logging.
        /// </summary>
        /// <param name="pLogMsg"></param>
        /// <param name="pLogType"></param>
        /// <param name="pPriority"></param>
        /// <param name="jobManagerTaskWk"></param>
        /// <param name="catalogOrderWk"></param>
        /// <modifications>
        /// Date          Author       IssueID            Description
        /// 08/02/2017    Rajeev       ODLM-1980          Log OnDemand request with no taskWk.
        /// </modifications>
        public static void LogInformation(string pLogMsg, DMELogType pLogType, DMELogPriority pPriority, int? jobManagerTaskWk = null, int? catalogOrderWk = null)
        {
            if (jobManagerTaskWk.HasValue)
            {
                if (jobManagerTaskWk.Value > 0)
                {
                    pLogMsg = string.Format("Stitching API : {0}", pLogMsg);
                    JobManagerAudit(jobManagerTaskWk.Value, JobManagerTaskMsgTypeEnum.Information, pLogMsg, null);
                }
            }
        }

        public static void LogInformation(string pLogMsg, DMELogType pLogType, DMELogPriority pPriority, object pDetails, int? jobManagerTaskWk = null, int? catalogOrderWk = null)
        {
            if (jobManagerTaskWk.HasValue)
            {
                if (jobManagerTaskWk.Value > 0)
                {
                    JobManagerAudit(jobManagerTaskWk.Value, JobManagerTaskMsgTypeEnum.Information, pLogMsg, pDetails);
                }
            }
        }

        public static void LogException(Exception pException, DMELogType pLogType, DMELogPriority pPriority, int? jobManagerTaskWk = null, int? catalogOrderWk = null)
        {
            if (jobManagerTaskWk.HasValue)
            {
                if (jobManagerTaskWk.Value > 0)
                {
                    JobManagerAudit(jobManagerTaskWk.Value, JobManagerTaskMsgTypeEnum.Error, pException.Message, null);
                }
            }

        }

        public static void LogException(Exception pException, DMELogType pLogType, DMELogPriority pPriority, object pDetails, int? jobManagerTaskWk = null, int? catalogOrderWk = null)
        {
            if (jobManagerTaskWk.HasValue)
            {
                if (jobManagerTaskWk.Value > 0)
                {
                    JobManagerAudit(jobManagerTaskWk.Value, JobManagerTaskMsgTypeEnum.Error, pException.Message, pDetails);
                }
            }

        }

        private static IDictionary<string, object> GetObjectGraphDictionary(object pDetails, bool convertObjectToDictionary = false)
        {
            // TODO: This is simplistic implementation that needs to be revisited, as time permits!!!

            // if the details are already a dictionary, then cast and assign it
            IDictionary<string, object> graph = null;
            if (pDetails is Dictionary<string, object>)
            {
                if (convertObjectToDictionary)
                {
                    Dictionary<string, object> dictionary = new Dictionary<string, object>();
                    foreach (string item in graph.Keys)
                    {
                        object o = dictionary[item];
                        if (o is Dictionary<string, object>)
                        {
                            dictionary.Add(item, o);
                        }
                        else
                        {
                            dictionary.Add(item, GetObjectGraphDictionary(o, true));
                        }
                    }
                    graph = dictionary;
                }
                else
                {
                    graph = (Dictionary<string, object>)pDetails;
                }
            }
            else
            {
                // if the details are from the exception object then convert it to a dictionary
                graph = new Dictionary<string, object>();
                if (pDetails is System.Collections.IEnumerable && pDetails.GetType().Name == "ListDictionaryInternal")
                {
                    var y = (pDetails as System.Collections.IEnumerable).GetEnumerator();
                    while (y.MoveNext())
                        if (y.Current is System.Collections.DictionaryEntry &&
                                ((System.Collections.DictionaryEntry)y.Current).Key is string
                            )
                            graph.Add((string)((System.Collections.DictionaryEntry)y.Current).Key, ((System.Collections.DictionaryEntry)y.Current).Value);
                }
                else
                {
                    // if it is an object then convert the properties to a dictionary
                    graph = ObjectPropertiesToDictionary(pDetails, convertObjectToDictionary);
                }
            }
            return graph;
        }

        private static string GetObjectGraphString(IDictionary<string, object> graph, int level = 0)
        {
            // TODO: This is simplistic implementation that needs to be revisited, as time permits!!!

            StringBuilder sb = new StringBuilder();
            foreach (string item in graph.Keys)
            {
                sb.Append("\n");
                if (level > 0)
                {
                    sb.Append(" ".PadLeft(level, '+'));
                }
                sb.AppendFormat("[{0}: ", item);

                object o = graph[item];
                if (o == null)
                {
                    sb.Append("<null>");
                }
                else if (o is Dictionary<string, object>)
                {
                    sb.Append(GetObjectGraphString(o as IDictionary<string, object>, level + 1));
                }
                else
                {
                    sb.Append(o.ToString());
                }

                sb.Append("]");
            }
            return sb.ToString();
        }

        private static string TryGetObjectGraphString(object pDetails)
        {
            // Expected/Assertions...
            Debug.Assert(pDetails != null);

            // Initialize object graph string
            string s = String.Empty;

            try
            {
                // Get object graph string
                IDictionary<string, object> graph = GetObjectGraphDictionary(pDetails, true);
                if (graph != null)
                {
                    s = GetObjectGraphString(graph);
                }
            }
            catch
            {
                // Yikes, but it is safer to ignore!!! But check this out if this happens in development/IDE environment!!!
                Debugger.Break();
            }

            // Return object graph string
            return s;
        }

        private static int JobManagerAudit(int taskWk, JobManagerTaskMsgTypeEnum messageType, string message, object pDetails, string userId = "System")
        {
            int taskMessageWk = int.MinValue;
            try
            {
                if (pDetails != null)
                {
                    string s = TryGetObjectGraphString(pDetails);
                    if (!String.IsNullOrEmpty(s))
                    {
                        //if (s.Length > DMELoggerExtension.WordWrapLength)
                        //{
                        //    s = s.WrapJobManagerAuditMessage(DMELoggerExtension.WordWrapLength, "] [", "]\n[");
                        //}
                        message = String.Format("{0}\n<{1}\n>", message, s);
                    }
                }

                using (var connection = new SqlConnection(ConfigurationHelper.config["ConnectionStrings:RRS_JobManager"]))
                {
                    SqlCommand command = new SqlCommand(@"dbo.uspCSLA_TaskMsg_Insert", connection);
                    command.CommandTimeout = 300; /* 5 minutes */
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@task_wk", taskWk);
                    command.Parameters.AddWithValue("@task_msg_type_name", messageType.ToString());
                    command.Parameters.AddWithValue("@msg", message);
                    command.Parameters.AddWithValue("@create_user", userId);

                    SqlParameter taskMessageWkParameter = new SqlParameter("@task_msg_wk", SqlDbType.Int) { Direction = ParameterDirection.Output };
                    command.Parameters.Add(taskMessageWkParameter);

                    connection.Open();
                    command.ExecuteNonQuery();

                    if (taskMessageWkParameter.Value != null)
                    {
                        if (!Int32.TryParse(taskMessageWkParameter.Value.ToString(), out taskMessageWk))
                        {
                            taskMessageWk = int.MinValue;
                        }
                    }
                }
            }
            catch (Exception)
            {

            }
            return taskMessageWk;
        }

        /// <summary>
        /// Purpose: OnDemand logging.
        /// </summary>
        /// <param name="catalogOrderWk"></param>
        /// <param name="messageType"></param>
        /// <param name="message"></param>
        /// <param name="pDetails"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        private static int OnDemandAudit(int catalogOrderWk, JobManagerTaskMsgTypeEnum messageType, string message, object pDetails, string userId = "System", string msgCategory = "Proof/Fulfullment")
        {
            int taskMessageWk = int.MinValue;
            try
            {
                if (pDetails != null)
                {
                    string s = TryGetObjectGraphString(pDetails);
                    if (!String.IsNullOrEmpty(s))
                    {
                        message = String.Format("{0}\n<{1}\n>", message, s);
                    }
                }

                using (SqlConnection connection = new SqlConnection(ConfigurationHelper.config["ConnectionStrings:RRS_VDF"]))
                {
                    SqlCommand command = new SqlCommand(@"audit.marketnow_log_add", connection);
                    command.CommandTimeout = 300 /* 5 minutes */;
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@catalog_order_wk", catalogOrderWk);
                    command.Parameters.AddWithValue("@severity", messageType.ToString());
                    command.Parameters.AddWithValue("@msg", message);
                    command.Parameters.AddWithValue("@log_user", userId);
                    command.Parameters.AddWithValue("@machine_name", System.Environment.MachineName);
                    command.Parameters.AddWithValue("@process_id", Process.GetCurrentProcess().Id);
                    command.Parameters.AddWithValue("@process_name", Process.GetCurrentProcess().ProcessName);
                    command.Parameters.AddWithValue("@thread_id", Thread.CurrentThread.ManagedThreadId);
                    command.Parameters.AddWithValue("@msg_category", msgCategory);

                    connection.Open();
                    command.ExecuteNonQuery();


                }
            }
            catch (Exception)
            {

            }
            return taskMessageWk;
        }


        private static Dictionary<string, object> ObjectPropertiesToDictionary(object pObj, bool convertObjectToDictionary = false)
        {
            // TODO: This is simplistic implementation that needs to be revisited, as time permits!!!

            Dictionary<string, object> myDict = new Dictionary<string, object>();

            try
            {
                foreach (PropertyInfo info in pObj.GetType().GetProperties())
                {
                    if (info.CanRead)
                    {
                        object o = info.GetValue(pObj, null);
                        if (o == null)
                        {
                            myDict.Add(info.Name, "<null>");
                        }
                        else if (convertObjectToDictionary)
                        {
                            Type dataType = o.GetType();
                            if (o is Dictionary<string, object>)
                            {
                                myDict.Add(info.Name, o);
                            }
                            else if (dataType.IsPrimitive)
                            {
                                myDict.Add(info.Name, o);
                            }
                            else if ((dataType == typeof(String)) || (dataType == typeof(Decimal)) || (dataType == typeof(DateTime)))
                            {
                                myDict.Add(info.Name, o);
                            }
                            else if (o is IEnumerable)
                            {
                                int index = 0;
                                IEnumerable enumerable = o as IEnumerable;
                                foreach (object item in enumerable)
                                {
                                    myDict.Add(String.Concat(info.Name, ".", index), item.ToString());
                                    index++;
                                }
                            }
                            else
                            {
                                myDict.Add(info.Name, ObjectPropertiesToDictionary(o, true));
                            }
                        }
                        else
                        {
                            myDict.Add(info.Name, o);
                        }
                    }
                } // foreach
            }
            catch
            {
                // TODO: Check this out!!!
                Debugger.Break();
                return null;
            }

            return myDict;
        } // ObjectPropertiesToDictionary

        private static string WrapJobManagerAuditMessage(this string t, int length, string delimiter, string delimiterReplacement)
        {
            string s = t;
            if (t != null)
            {
                if (t.Length > length)
                {
                    StringBuilder sb = new StringBuilder();
                    StringBuilder buffer = new StringBuilder();
                    string[] split = t.Split(new string[] { delimiter }, StringSplitOptions.None);
                    for (int i = 0; i < split.Length; i++)
                    {
                        string item = split[i];
                        bool lastToken = (i == (split.Length - 1));

                        buffer.Append(item);
                        if (buffer.Length + delimiter.Length > length)
                        {
                            sb.Append(buffer);
                            if (!lastToken)
                            {
                                sb.Append(delimiterReplacement);
                                buffer.Clear();
                            }
                        }
                        else if (!lastToken)
                        {
                            buffer.Append(delimiter);
                        }
                    }
                    if (buffer.Length > 0)
                    {
                        sb.Append(buffer);
                    }
                    s = sb.ToString();
                }
            }
            return s;
        }
    }
}
