﻿using AutoPoint.Stitching.Common;
using AutoPoint.Stitching.Common.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace AutoPoint.Stitching.Api.Middleware
{
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        ///     Adds Retry Policy
        /// </summary>
        /// <param name="services"></param>
        /// <param name="contextLifetime"></param>
        /// <returns></returns>
        public static IServiceCollection AddRetryPolicy(this IServiceCollection services,
        ServiceLifetime contextLifetime = ServiceLifetime.Transient)
        {
            if (contextLifetime == ServiceLifetime.Scoped)
                services.AddScoped<IRetryPolicy, DbRetryPolicy>();
            else if (contextLifetime == ServiceLifetime.Singleton)
                services.AddSingleton<IRetryPolicy, DbRetryPolicy>();
            else if (contextLifetime == ServiceLifetime.Transient) services.AddTransient<IRetryPolicy, DbRetryPolicy>();
            return services;
        }
    }
}
