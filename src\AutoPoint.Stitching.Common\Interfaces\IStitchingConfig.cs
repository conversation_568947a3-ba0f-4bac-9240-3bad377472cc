﻿namespace AutoPoint.Stitching.Common.Interfaces
{
    public interface IStitchingConfig
    {
        int DefaultBatchSize { get; set; }
        int DefaultPagesPerContact { get; set; }
        bool OnDemandStitchDeleteOriginals { get; set; }
        string OnDemandFileStore_PDFO { get; set; }
        string OnDemandFileStore_PDFVT { get; set; }
        string OnDemandFileStore_CampaignProofs { get; set; }
        int MergeFileContacts { get; set; }
        int MergeFileSaveThreads { get; set; }
        string OnDemandFile_OutPutType { get; set; }
        string OnDemandFileStore_Local { get; set; }
        int OnDemandStitchDiskSpaceInMB { get; set; }
        int OnDemandStitchCPUThresholdPercent { get; set; }
        int OnDemandStitchRAMThresholdPercent { get; set; }
        int OnDemandStitchJobsThreshold { get; set; }
        int SingeOrderCIFStitchingThreshold { get; set; }
        string MergedCompleteFilename { get; set; }
        bool NewRelicAgentEnabled { get; set; }
        string NewRelicAppName { get; set; }
        string NewRelicLicenseKey { get; set; }
        string PDFTronKey { get; set; }
        string ContactSheetHeaderFilePath { get; set; }
        bool VerboseLogging { get; set; }
        string OnDemandFileStore_UNC { get; set; }
    }
}
