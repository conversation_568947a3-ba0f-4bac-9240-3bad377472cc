﻿using System.Collections.Generic;

namespace AutoPoint.Stitching.Common.Models
{
    public class PDFMergeMailStreamOrderRequest
    {
        public XmpieJobWorkItem WorkItem { get; set; }
        public string[] InputFiles { get; set; }
        public string OutputFile { get; set; }
        public bool DeleteOriginals { get; set; }
        public bool SourceLocationOverride { get; set; }
        public string OverrideLocation { get; set; }
        public int? MergeBatchsize { get; set; } = null;
        public bool CommingleOnly { get; set; }
        public bool DeleteInputFiles { get; set; }
        public string DocId { get; set; } = string.Empty;
        public string FileAppend { get; set; } = string.Empty;
        public List<string> DeleteFiles { get; set; } = new List<string> { };
        public int PerContactPages { get; set; } = 2;
        public int? TaskWk { get; set; }
        public int? TaskAbortRequestWk { get; set; }
    }
}
