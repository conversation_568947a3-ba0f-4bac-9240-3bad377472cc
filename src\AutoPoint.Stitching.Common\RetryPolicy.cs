﻿using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using System.Threading;
using Microsoft.Extensions.Logging;

namespace AutoPoint.Stitching.Common
{
    [ExcludeFromCodeCoverage]
    public class RetryPolicy
    {

        public const int Default_Retry_Limit = 3;
        public const int Default_Retry_Delay = 500; //milliseconds
        protected readonly ILogger Logger;

        public int RetryLimit { get; protected set; }

        public int RetryDelay { get; protected set; }

        public RetryPolicy(ILogger logger, int retryLimit = Default_Retry_Limit, int retryDelay = Default_Retry_Delay)
        {
            Logger = logger;
            RetryLimit = retryLimit;
            RetryDelay = retryDelay;
        }


        public async Task ExecuteAsync(Func<CancellationToken, Task> func, CancellationToken token = default)
        {
            Logger?.LogInformation($"\"Class\": \"{nameof(RetryPolicy)}\", \"Operation\" : \"{nameof(RetryPolicy.ExecuteAsync)}\"");
            await ExecuteAsync(func, 0, token);
        }

        public void Execute(Action action)
        {
            Logger?.LogInformation($"\"Class\": \"{nameof(RetryPolicy)}\", \"Operation\" : \"{nameof(RetryPolicy.Execute)}\"");
            Execute(action, 0, null);
        }
        public async Task<TResult> Execute<TResult>(Func<Task<TResult>> action)
        {
            Logger?.LogInformation($"\"Class\": \"{nameof(RetryPolicy)}\", \"Operation\" : \"{nameof(RetryPolicy.ExecuteAsync)}\"");
            return await Execute(action, 0, null);
        }
        public async Task<TResult> ExecuteAsync<TResult>(Func<CancellationToken, Task<TResult>> func, CancellationToken token = default)
        {
            Logger?.LogInformation($"\"Class\": \"{nameof(RetryPolicy)}\", \"Operation\" : \"{nameof(RetryPolicy.ExecuteAsync)}\"");
            return await ExecuteAsync(func, 0, token);
        }
        public TResult Execute<TResult>(Func<TResult> action)
        {
            Logger?.LogInformation($"\"Class\": \"{nameof(RetryPolicy)}\", \"Operation\" : \"{nameof(RetryPolicy.ExecuteAsync)}\"");
            return Execute(action, 0, null);
        }
        private async Task ExecuteAsync(Func<CancellationToken, Task> func, int retryCount, CancellationToken token = default, Exception lastException = null)
        {
            Logger?.LogInformation($"\"Class\": \"{nameof(RetryPolicy)}\", \"Operation\" : \"{nameof(RetryPolicy.ExecuteAsync)}_2\",  \"retryCount\": \"{retryCount}\"");
            var funcHandle = func;
            if (retryCount <= RetryLimit)
            {
                try
                {
                    if (funcHandle != null && !token.IsCancellationRequested)
                    {
                        await funcHandle(token);
                    }
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, $"Execution Failed. Retry number {retryCount}. Retry again in {RetryDelay} milliseconds");
                    retryCount++;
                    await Task.Delay(RetryDelay);
                    await ExecuteAsync(funcHandle, retryCount, token, ex);
                }
            }
            else
            {
                Logger?.LogInformation($"\"Class\": \"{nameof(RetryPolicy)}\", \"Operation\" : \"{nameof(RetryPolicy.ExecuteAsync)}_2\",  \"retryCount\": \"{retryCount}\"");
                var retryPolicyException = new RetryPolicyException($"Attempted to re-execute {retryCount - 1} times", func, lastException);
                Logger?.LogError(retryPolicyException, "Unable to execute function");
                throw retryPolicyException;
            }
        }

        private async Task<TResult> ExecuteAsync<TResult>(Func<CancellationToken, Task<TResult>> action, int retryCount, CancellationToken token = default, Exception lastException = null)
        {
            Logger?.LogInformation($"\"Class\": \"{nameof(RetryPolicy)}\", \"Operation\" : \"{nameof(RetryPolicy.ExecuteAsync)}_3\", \"retryCount\": \"{retryCount}\"");
            var funcHandle = action;
            TResult response = default;

            if (retryCount <= RetryLimit)
            {
                try
                {
                    if (funcHandle != null && !token.IsCancellationRequested)
                    {
                        response = await funcHandle(token);
                    }
                }
                catch (Exception ex)
                {
                    retryCount++;

                    Logger?.LogError(ex, $"Execution Failed. Retry number {retryCount}. Retry again in {RetryDelay} milliseconds");
                    await Task.Delay(RetryDelay);
                    await ExecuteAsync(funcHandle, retryCount, token, ex);

                }
            }
            else
            {
                Logger?.LogInformation($"\"Class\": \"{nameof(RetryPolicy)}\", \"Operation\" : \"{nameof(RetryPolicy.ExecuteAsync)}_3\",  \"retryCount\": \"{retryCount}\"");
                var retryPolicyException = new RetryPolicyException($"Attempted to re-execute {retryCount - 1} times", action, lastException);
                Logger?.LogError(retryPolicyException, "Unable to execute function");
                throw retryPolicyException;
            }
            return response;
        }

        private void Execute(Action action, int retryCount, Exception lastException = null)
        {
            Logger?.LogInformation($"\"Class\": \"{nameof(RetryPolicy)}\", \"Operation\" : \"{nameof(RetryPolicy.Execute)}_2\",  \"retryCount\": \"{retryCount}\"");
            var actionHandle = action;
            if (retryCount <= RetryLimit && actionHandle != null)
            {
                try
                {
                    actionHandle();
                }
                catch (Exception ex)
                {
                    retryCount++;
                    Logger?.LogError(ex, $"Execution Failed. Retry number {retryCount}. Retry again in {RetryDelay} milliseconds");
                    Thread.Sleep(RetryDelay);
                    Execute(actionHandle, retryCount, ex);
                }
            }
            else
            {
                Logger?.LogInformation($"\"Class\": \"{nameof(RetryPolicy)}\", \"Operation\" : \"{nameof(RetryPolicy.Execute)}_2\",  \"retryCount\": \"{retryCount}\"");

                var retryPolicyException = new RetryPolicyException($"Attempted to re-execute {retryCount - 1} times", action, lastException);
                Logger?.LogError(retryPolicyException, "Unable to execute action");
                throw retryPolicyException;
            }
        }

        private TResult Execute<TResult>(Func<TResult> actionResult, int retryCount, Exception lastException = null)
        {
            Logger?.LogInformation($"\"Class\": \"{nameof(RetryPolicy)}\", \"Operation\" : \"{nameof(RetryPolicy.Execute)}_2\",  \"retryCount\": \"{retryCount}\"");
            var actionHandleResult = actionResult;
            if (retryCount <= RetryLimit && actionHandleResult != null)
            {
                try
                {
                    return actionHandleResult();
                }
                catch (Exception ex)
                {
                    retryCount++;
                    Logger?.LogError(ex, $"Execution Failed. Retry number {retryCount}. Retry again in {RetryDelay} milliseconds");
                    Thread.Sleep(RetryDelay);
                    Execute(actionHandleResult, retryCount, ex);
                }
            }
            else
            {
                Logger?.LogInformation($"\"Class\": \"{nameof(RetryPolicy)}\", \"Operation\" : \"{nameof(RetryPolicy.Execute)}_2\",  \"retryCount\": \"{retryCount}\"");

                var retryPolicyException = new RetryPolicyException($"Attempted to re-execute {retryCount - 1} times", actionResult, lastException);
                Logger?.LogError(retryPolicyException, "Unable to execute action");
                throw retryPolicyException;
            }
            return actionHandleResult();
        }

        private Task<TResult> Execute<TResult>(Func<Task<TResult>> actionTask, int retryCount, Exception lastException = null)
        {

            Logger?.LogInformation($"\"Class\": \"{nameof(RetryPolicy)}\", \"Operation\" : \"{nameof(RetryPolicy.Execute)}_2\",  \"retryCount\": \"{retryCount}\"");
            if (retryCount <= RetryLimit && actionTask != null)
            {
                try
                {
                    return actionTask();
                }
                catch (Exception ex)
                {
                    retryCount++;
                    Logger?.LogError(ex, $"Execution Failed. Retry number {retryCount}. Retry again in {RetryDelay} milliseconds");
                    Thread.Sleep(RetryDelay);
                    Execute(actionTask, retryCount, ex);
                }
            }
            else
            {
                Logger?.LogInformation($"\"Class\": \"{nameof(RetryPolicy)}\", \"Operation\" : \"{nameof(RetryPolicy.Execute)}_2\",  \"retryCount\": \"{retryCount}\"");

                var retryPolicyException = new RetryPolicyException($"Attempted to re-execute {retryCount - 1} times", actionTask, lastException);
                Logger?.LogError(retryPolicyException, "Unable to execute action");
                throw retryPolicyException;
            }
            return actionTask();
        }

    }
}
