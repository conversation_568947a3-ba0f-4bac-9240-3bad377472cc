﻿using System;

namespace AutoPoint.Stitching.Common.Enums
{
    public enum XmpieRotatePages
    {
        None = -1,
        Even,
        <PERSON>
    }

    public static class XmpieRotatePagesExtension
    {
        public static string GetName(this XmpieOutputType x)
        {
            return Enum.GetName(x.GetType(), x);
        }

        public static string GetName<T>(this T x)
        {
            return Enum.GetName(x.GetType(), x);
        }
    }
}
