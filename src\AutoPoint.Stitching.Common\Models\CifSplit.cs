﻿using AutoPoint.Stitching.Common.Enums;
using System;

namespace AutoPoint.Stitching.Common.Models
{
    public class CifSplit
    {
        public int CifSplitWk { get; set; }
        public int CifSplitDefnWk { get; set; }
        public int AuditWk { get; set; }
        public string SplitName { get; set; }
        public int Mailing { get; set; }
        public string ReferenceId { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int StartRecordCount { get; set; }
        public int EndRecordCount { get; set; }
        public int ErrorRecordCount { get; set; }
        public string XmpieJobId { get; set; }
        public string XmpieJobProofId { get; set; }
        public int SourceJobWk { get; set; }
        public int VDFJobWk { get; set; }
        public bool IsMixedWeightPresort { get; set; }
        public bool IsSortOverride { get; set; }
        public bool IncludeCouponContactSheet { get; set; }
        public int RotateProofPages { get; set; }
        public int Rotation { get; set; }
        public bool IsManifestMailing { get; set; }
        public bool DealerVersion { get; set; }
        public bool IsTestMode { get; set; }
        public bool IsIPN { get; set; }

        public XmpieRotatePages TranslateRotation()
        {
            XmpieRotatePages rotatePages = XmpieRotatePages.None;
            switch (this.RotateProofPages)
            {
                case 0:
                    rotatePages = XmpieRotatePages.Even;
                    break;

                case 1:
                    rotatePages = XmpieRotatePages.Odd;
                    break;

                default:
                    break;
            }

            return rotatePages;
        }
    }
}
