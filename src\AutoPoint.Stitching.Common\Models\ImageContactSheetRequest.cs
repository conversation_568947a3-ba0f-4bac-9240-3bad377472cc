﻿namespace AutoPoint.Stitching.Common.Models
{
    public class ImageContactSheetRequest
    {

        public int ImageContactSheetRequestWk { get; set; }
        public int JobWk { get; set; }
        public int CampaignTypeWk { get; set; }
        public int OrgWk { get; set; }
        public string CampaignTypeName { get; set; }
        public int ImageWk { get; set; }
        public string ImageCode { get; set; }
        public string ImageDisplayName { get; set; }
        public int CategoryWk { get; set; }
        public string CategoryName { get; set; }
        public string Organization { get; set; }
        public string Dimensions { get; set; }
        public string Status { get; set; }
        public string ContactSheetPath { get; set; }
        public string ImagePath { get; set; }
        public string ImageName { get; set; }
        public int ImageHeight { get; set; }
        public int ImageWidth { get; set; }
        public int ImageSize { get; set; }
        public decimal ImageAverageHeight { get; set; }
        public decimal ImageAverageWidth { get; set; }
        public string OutputFolderPath { get; set; }
        public string GeneratedFileName { get; set; }
        public string SubmitUser { get; set; }
        public string ProofEmailAddress { get; set; }
        public string ImageUrl { get; set; }
    }
}
