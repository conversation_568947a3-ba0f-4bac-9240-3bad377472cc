﻿using System;
using System.Collections.Generic;

namespace AutoPoint.Stitching.Common
{
    public class RetryStrategy
    {
        /// <summary>
        ///     Retry count
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        ///     Time in milliseconds
        /// </summary>
        public int WaitBetweenRetries { get; set; } = 500;

        /// <summary>
        ///     User defined intervals
        /// </summary>
        public IEnumerable<TimeSpan> Intervals { get; set; } = new List<TimeSpan>();

        /// <summary>
        ///     Retry Strategy Type
        /// </summary>
        public RetryStrategyType Type { get; set; }
    }

    public enum RetryStrategyType
    {
        /// <summary>
        ///		ExponentialBackoffWithJitter
        /// </summary>
        ExponentialBackoffWithJitter = 0,

        /// <summary>
        ///		ExponentialBackoff
        /// </summary>
        ExponentialBackoff = 1,

        /// <summary>
        ///		Basic
        /// </summary>
        Basic = 2,

        /// <summary>
        ///		UserDefined
        /// </summary>
        UserDefined = 3
    }
}
