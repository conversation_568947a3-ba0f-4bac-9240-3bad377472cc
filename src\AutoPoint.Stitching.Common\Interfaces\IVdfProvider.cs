﻿using AutoPoint.Stitching.Common.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AutoPoint.Stitching.Common.Interfaces
{
    public interface IVdfProvider
    {
        Task<MailstreamStitchConfig> GetOnDemandMailStreamStitchConfig(int jobWk, string outputType);
        Task<IList<Mailstream2Filename>> GetMailstreamFilenames(int jobWk, int batchSize, string outputFolder, bool commingleOnly, string fileAppend = "");
        Task<IList<ODPCommunicationOrder>> GetODPFilesInCommOrder(int jobWk, string marketingStrategyName, int numPages);
        Task<SortGroup> GetSortGroup(int vdfJobWk);
        Task<IList<MarketNowDIFProofXmpie>> GetOnDemandDIFProofXmpieJobs(int jobWk);
        Task<bool> UpdateTaskStatusTracking(int taskTrackingRequestWk, string statusInd, string statusDesc);
        Task<bool> OnDemandMergeFileMailStreamAddRequests(int jobWk, int taskWk, bool isCommingle, string requestJson);
        Task<bool> OnDemandMergeFileMailStreamDeleteRequests(int jobWk, int taskWk, bool isCommingle);
        Task<bool> OnDemandMergeFileMailStreamUpdateStatus(int jobWk, int taskWk, bool isCommingle, int fileIndex, string statusIndicator, string msg, int retries);
        Task<IList<Mailstream2FilenameResponse>> GetOnDemandMergeFileMailStreamRequests(int jobWk, int taskWk, bool isCommingle);
    }
}
