﻿using System;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Serilog;

namespace AutoPoint.Stitching.Api.Middleware
{
    [ExcludeFromCodeCoverage]
    public class ExceptionMiddleware
    {
        private readonly RequestDelegate _next;

        public ExceptionMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        /// <summary>
        /// Purpose: Exception logging all application exceptions reach here
        /// </summary>
        /// <returns>Chrome data</returns>
        public async Task Invoke(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (ArgumentNullException ex)
            {
                context.Response.ContentType = "text/plain";
                context.Response.StatusCode = (int)HttpStatusCode.BadRequest;

                Log.Warning(string.Format("{0}" + Environment.NewLine + "{1}", context.Request.Path, ex.Message));

                await context.Response.WriteAsync(ex.Message);
            }
            catch (Exception ex)
            {
                context.Response.ContentType = "text/plain";
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;

                Log.Error(string.Format("{0}" + Environment.NewLine + "{1}" + Environment.NewLine + "{2}", context.Request.Path, ex.Message, ex.StackTrace));

                await context.Response.WriteAsync(ex.Message);
            }
        }
    }
}
