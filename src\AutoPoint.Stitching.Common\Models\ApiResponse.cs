﻿using System.Diagnostics.CodeAnalysis;

namespace AutoPoint.Stitching.Common.Models
{
    /// <summary>
	///     Api Response
	/// </summary>
	[ExcludeFromCodeCoverage]
    public class ApiResponse
    {
        /// <summary>
        ///     Success indicator
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        ///     Message
        /// </summary>
        public string Message { get; set; }
    }
}
