﻿using System.Diagnostics.CodeAnalysis;
using System.Security.Principal;

namespace AutoPoint.Stitching.Api.Auth
{
    [ExcludeFromCodeCoverage]
    public class CustomIdentity : GenericIdentity
    {
        public CustomIdentity(string name) : base(name)
        {
        }

        public CustomIdentity(string name, string type) : base(name, type)
        {
        }

        public CustomIdentity(GenericIdentity identity) : base(identity)
        {
        }
    }
}
