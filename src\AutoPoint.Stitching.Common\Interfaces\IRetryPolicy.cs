﻿using System;
using System.Threading.Tasks;
using System.Threading;

namespace AutoPoint.Stitching.Common.Interfaces
{
    public interface IRetryPolicy
    {
        RetryPolicyType RetryPolicyType { get; }

        void Execute(Action operation, RetryStrategy retryStrategy = null);

        TResult Execute<TResult>(Func<TResult> operation, RetryStrategy retryStrategy = null);

        Task ExecuteAsync(Func<CancellationToken, Task> operation, CancellationToken cancellationToken,
            RetryStrategy retryStrategy = null);

        Task<TResult> ExecuteAsync<TResult>(Func<CancellationToken, Task<TResult>> operation,
            CancellationToken cancellationToken, RetryStrategy retryStrategy = null);
    }
}
