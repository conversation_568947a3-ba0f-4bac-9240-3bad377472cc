﻿using System;

namespace AutoPoint.Stitching.Common.Models
{
    public class Mailstream2Filename
    {
        public int rowNumber { get; set; }
        public int batchNumber { get; set; }
        public long cifWk { get; set; }
        public int presortId { get; set; }
        public string filename { get; set; }

        public int GetFileIndex(string[] inputFiles)
        {
            var recordFileLocationIndex = -1;
            recordFileLocationIndex = Array.FindIndex(inputFiles, x => x.Equals(filename.Replace("\\", @"\").ToLower()));
            if (recordFileLocationIndex == -1)
                recordFileLocationIndex = Array.FindIndex(inputFiles, x => x.Equals(filename.ToLower()));
            if (recordFileLocationIndex == -1)
                recordFileLocationIndex = Array.FindIndex(inputFiles, x => x.Equals(filename.ToLower().Replace("_1.pdf", ".pdf")));

            return recordFileLocationIndex;
        }
    }

    public class Mailstream2FilenameDto
    {
        public int row_no { get; set; }
        public int batch_no { get; set; }
        public long cif_wk { get; set; }
        public int presort_id { get; set; }
        public string xmpie_filename { get; set; }
    }

    public class Mailstream2FilenameRequest
    {
        public int JobWk { get; set; }
        public int FileIndex { get; set; }
        public bool IsCommingle { get; set; }
        public int MinPresortId { get; set; }
        public int MaxPresortId { get; set; }
    }

    public class Mailstream2FilenameResponseDto
    {
        public int job_wk { get; set; }
        public int file_index { get; set; }
        public bool is_commingle { get; set; }
        public int min_presort_id { get; set; }
        public int max_presort_id { get; set; }
        public string status_indicator { get; set; }
        public string msg { get; set; }
        public int? retries { get; set; }
    }

    public class Mailstream2FilenameResponse
    {
        public int JobWk { get; set; }
        public int FileIndex { get; set; }
        public bool IsCommingle { get; set; }
        public int MinPresortId { get; set; }
        public int MaxPresortId { get; set; }
        public string StatusIndicator { get; set; }
        public string Message { get; set; }
        public int? Retries { get; set; }
    }
}
