{"Version": 1, "WorkspaceRootPath": "C:\\bitbucket\\stitching-api\\src\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{5D380C07-A0FA-4BD1-A1A0-9CD99BE06D76}|AutoPoint.Stitching.Services\\AutoPoint.Stitching.Services.csproj|c:\\bitbucket\\stitching-api\\src\\autopoint.stitching.services\\stitchingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5D380C07-A0FA-4BD1-A1A0-9CD99BE06D76}|AutoPoint.Stitching.Services\\AutoPoint.Stitching.Services.csproj|solutionrelative:autopoint.stitching.services\\stitchingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B873F5B7-9DD4-473F-88D2-6583F05B4655}|AutoPoint.Stitching.Api\\AutoPoint.Stitching.Api.csproj|c:\\bitbucket\\stitching-api\\src\\autopoint.stitching.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B873F5B7-9DD4-473F-88D2-6583F05B4655}|AutoPoint.Stitching.Api\\AutoPoint.Stitching.Api.csproj|solutionrelative:autopoint.stitching.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{59BB6CF8-C91C-4F3F-85EE-6C827504F496}|AutoPoint.Stitching.Common\\AutoPoint.Stitching.Common.csproj|c:\\bitbucket\\stitching-api\\src\\autopoint.stitching.common\\models\\stitchingconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{59BB6CF8-C91C-4F3F-85EE-6C827504F496}|AutoPoint.Stitching.Common\\AutoPoint.Stitching.Common.csproj|solutionrelative:autopoint.stitching.common\\models\\stitchingconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{59BB6CF8-C91C-4F3F-85EE-6C827504F496}|AutoPoint.Stitching.Common\\AutoPoint.Stitching.Common.csproj|c:\\bitbucket\\stitching-api\\src\\autopoint.stitching.common\\interfaces\\istitchingconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{59BB6CF8-C91C-4F3F-85EE-6C827504F496}|AutoPoint.Stitching.Common\\AutoPoint.Stitching.Common.csproj|solutionrelative:autopoint.stitching.common\\interfaces\\istitchingconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B873F5B7-9DD4-473F-88D2-6583F05B4655}|AutoPoint.Stitching.Api\\AutoPoint.Stitching.Api.csproj|c:\\bitbucket\\stitching-api\\src\\autopoint.stitching.api\\appsettings.production.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B873F5B7-9DD4-473F-88D2-6583F05B4655}|AutoPoint.Stitching.Api\\AutoPoint.Stitching.Api.csproj|solutionrelative:autopoint.stitching.api\\appsettings.production.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{B873F5B7-9DD4-473F-88D2-6583F05B4655}|AutoPoint.Stitching.Api\\AutoPoint.Stitching.Api.csproj|c:\\bitbucket\\stitching-api\\src\\autopoint.stitching.api\\appsettings.staging.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B873F5B7-9DD4-473F-88D2-6583F05B4655}|AutoPoint.Stitching.Api\\AutoPoint.Stitching.Api.csproj|solutionrelative:autopoint.stitching.api\\appsettings.staging.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{66685D6D-4897-4B05-B172-C8FEB56801CF}|AutoPoint.Stitching.DataProviders\\AutoPoint.Stitching.DataProviders.csproj|c:\\bitbucket\\stitching-api\\src\\autopoint.stitching.dataproviders\\vdfprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{66685D6D-4897-4B05-B172-C8FEB56801CF}|AutoPoint.Stitching.DataProviders\\AutoPoint.Stitching.DataProviders.csproj|solutionrelative:autopoint.stitching.dataproviders\\vdfprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{59BB6CF8-C91C-4F3F-85EE-6C827504F496}|AutoPoint.Stitching.Common\\AutoPoint.Stitching.Common.csproj|c:\\bitbucket\\stitching-api\\src\\autopoint.stitching.common\\interfaces\\ivdfprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{59BB6CF8-C91C-4F3F-85EE-6C827504F496}|AutoPoint.Stitching.Common\\AutoPoint.Stitching.Common.csproj|solutionrelative:autopoint.stitching.common\\interfaces\\ivdfprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{59BB6CF8-C91C-4F3F-85EE-6C827504F496}|AutoPoint.Stitching.Common\\AutoPoint.Stitching.Common.csproj|c:\\bitbucket\\stitching-api\\src\\autopoint.stitching.common\\logger\\dmeloggerextension.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{59BB6CF8-C91C-4F3F-85EE-6C827504F496}|AutoPoint.Stitching.Common\\AutoPoint.Stitching.Common.csproj|solutionrelative:autopoint.stitching.common\\logger\\dmeloggerextension.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B873F5B7-9DD4-473F-88D2-6583F05B4655}|AutoPoint.Stitching.Api\\AutoPoint.Stitching.Api.csproj|c:\\bitbucket\\stitching-api\\src\\autopoint.stitching.api\\controllers\\stitchingcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B873F5B7-9DD4-473F-88D2-6583F05B4655}|AutoPoint.Stitching.Api\\AutoPoint.Stitching.Api.csproj|solutionrelative:autopoint.stitching.api\\controllers\\stitchingcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{59BB6CF8-C91C-4F3F-85EE-6C827504F496}|AutoPoint.Stitching.Common\\AutoPoint.Stitching.Common.csproj|c:\\bitbucket\\stitching-api\\src\\autopoint.stitching.common\\interfaces\\istitchingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{59BB6CF8-C91C-4F3F-85EE-6C827504F496}|AutoPoint.Stitching.Common\\AutoPoint.Stitching.Common.csproj|solutionrelative:autopoint.stitching.common\\interfaces\\istitchingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{59BB6CF8-C91C-4F3F-85EE-6C827504F496}|AutoPoint.Stitching.Common\\AutoPoint.Stitching.Common.csproj|c:\\bitbucket\\stitching-api\\src\\autopoint.stitching.common\\models\\pdfmergemailstreamorderrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{59BB6CF8-C91C-4F3F-85EE-6C827504F496}|AutoPoint.Stitching.Common\\AutoPoint.Stitching.Common.csproj|solutionrelative:autopoint.stitching.common\\models\\pdfmergemailstreamorderrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B873F5B7-9DD4-473F-88D2-6583F05B4655}|AutoPoint.Stitching.Api\\AutoPoint.Stitching.Api.csproj|c:\\bitbucket\\stitching-api\\src\\autopoint.stitching.api\\logprovider\\serilogsetup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B873F5B7-9DD4-473F-88D2-6583F05B4655}|AutoPoint.Stitching.Api\\AutoPoint.Stitching.Api.csproj|solutionrelative:autopoint.stitching.api\\logprovider\\serilogsetup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 12, "Children": [{"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:132:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:131:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:133:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 5, "Title": "appsettings.Staging.json", "DocumentMoniker": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Api\\appsettings.Staging.json", "RelativeDocumentMoniker": "AutoPoint.Stitching.Api\\appsettings.Staging.json", "ToolTip": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Api\\appsettings.Staging.json", "RelativeToolTip": "AutoPoint.Stitching.Api\\appsettings.Staging.json", "ViewState": "AQIAAAAAAAAAAAAAAAAAAA0AAAABAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-10-14T19:20:44.086Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "appsettings.Production.json", "DocumentMoniker": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Api\\appsettings.Production.json", "RelativeDocumentMoniker": "AutoPoint.Stitching.Api\\appsettings.Production.json", "ToolTip": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Api\\appsettings.Production.json", "RelativeToolTip": "AutoPoint.Stitching.Api\\appsettings.Production.json", "ViewState": "AQIAAAAAAAAAAAAAAAAAAA4AAAAaAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-10-14T17:39:55.814Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.json", "DocumentMoniker": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Api\\appsettings.json", "RelativeDocumentMoniker": "AutoPoint.Stitching.Api\\appsettings.json", "ToolTip": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Api\\appsettings.json", "RelativeToolTip": "AutoPoint.Stitching.Api\\appsettings.json", "ViewState": "AQIAABAAAAAAAAAAAAAAwDAAAAAWAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-08-13T18:40:38.385Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "StitchingConfig.cs", "DocumentMoniker": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Common\\Models\\StitchingConfig.cs", "RelativeDocumentMoniker": "AutoPoint.Stitching.Common\\Models\\StitchingConfig.cs", "ToolTip": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Common\\Models\\StitchingConfig.cs", "RelativeToolTip": "AutoPoint.Stitching.Common\\Models\\StitchingConfig.cs", "ViewState": "AQIAABAAAAAAAAAAAAAgwCcAAAArAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-26T13:20:50.454Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "IStitchingConfig.cs", "DocumentMoniker": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Common\\Interfaces\\IStitchingConfig.cs", "RelativeDocumentMoniker": "AutoPoint.Stitching.Common\\Interfaces\\IStitchingConfig.cs", "ToolTip": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Common\\Interfaces\\IStitchingConfig.cs", "RelativeToolTip": "AutoPoint.Stitching.Common\\Interfaces\\IStitchingConfig.cs", "ViewState": "AQIAAAEAAAAAAAAAAAAywAsAAAAuAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-26T13:20:38.639Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "StitchingService.cs", "DocumentMoniker": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Services\\StitchingService.cs", "RelativeDocumentMoniker": "AutoPoint.Stitching.Services\\StitchingService.cs", "ToolTip": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Services\\StitchingService.cs", "RelativeToolTip": "AutoPoint.Stitching.Services\\StitchingService.cs", "ViewState": "AQIAAKsAAAAAAAAAAAAWwLIAAAAiAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-08-13T17:38:51.226Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "DMELoggerExtension.cs", "DocumentMoniker": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Common\\Logger\\DMELoggerExtension.cs", "RelativeDocumentMoniker": "AutoPoint.Stitching.Common\\Logger\\DMELoggerExtension.cs", "ToolTip": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Common\\Logger\\DMELoggerExtension.cs", "RelativeToolTip": "AutoPoint.Stitching.Common\\Logger\\DMELoggerExtension.cs", "ViewState": "AQIAABsAAAAAAAAAAADwvywAAAAQAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-10T02:09:59.089Z"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 7, "Title": "IVdfProvider.cs", "DocumentMoniker": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Common\\Interfaces\\IVdfProvider.cs", "RelativeDocumentMoniker": "AutoPoint.Stitching.Common\\Interfaces\\IVdfProvider.cs", "ToolTip": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Common\\Interfaces\\IVdfProvider.cs", "RelativeToolTip": "AutoPoint.Stitching.Common\\Interfaces\\IVdfProvider.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAsAAAAzAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-08-25T02:17:09.204Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "IStitchingService.cs", "DocumentMoniker": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Common\\Interfaces\\IStitchingService.cs", "RelativeDocumentMoniker": "AutoPoint.Stitching.Common\\Interfaces\\IStitchingService.cs", "ToolTip": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Common\\Interfaces\\IStitchingService.cs", "RelativeToolTip": "AutoPoint.Stitching.Common\\Interfaces\\IStitchingService.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAkAAAAUAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-08-25T02:22:58.649Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "VdfProvider.cs", "DocumentMoniker": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.DataProviders\\VdfProvider.cs", "RelativeDocumentMoniker": "AutoPoint.Stitching.DataProviders\\VdfProvider.cs", "ToolTip": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.DataProviders\\VdfProvider.cs", "RelativeToolTip": "AutoPoint.Stitching.DataProviders\\VdfProvider.cs", "ViewState": "AQIAANUAAAAAAAAAAIA/wGABAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-08-13T17:35:04.466Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "StitchingController.cs", "DocumentMoniker": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Api\\Controllers\\StitchingController.cs", "RelativeDocumentMoniker": "AutoPoint.Stitching.Api\\Controllers\\StitchingController.cs", "ToolTip": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Api\\Controllers\\StitchingController.cs", "RelativeToolTip": "AutoPoint.Stitching.Api\\Controllers\\StitchingController.cs", "ViewState": "AQIAAAQAAAAAAAAAAAAuwBsAAABHAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-08-13T13:04:13.806Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "SeriLogSetup.cs", "DocumentMoniker": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Api\\LogProvider\\SeriLogSetup.cs", "RelativeDocumentMoniker": "AutoPoint.Stitching.Api\\LogProvider\\SeriLogSetup.cs", "ToolTip": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Api\\LogProvider\\SeriLogSetup.cs", "RelativeToolTip": "AutoPoint.Stitching.Api\\LogProvider\\SeriLogSetup.cs", "ViewState": "AQIAAAMAAAAAAAAAAAAAAAgAAAAtAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-08-20T21:03:35.683Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "PDFMergeMailStreamOrderRequest.cs", "DocumentMoniker": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Common\\Models\\PDFMergeMailStreamOrderRequest.cs", "RelativeDocumentMoniker": "AutoPoint.Stitching.Common\\Models\\PDFMergeMailStreamOrderRequest.cs", "ToolTip": "C:\\bitbucket\\stitching-api\\src\\AutoPoint.Stitching.Common\\Models\\PDFMergeMailStreamOrderRequest.cs", "RelativeToolTip": "AutoPoint.Stitching.Common\\Models\\PDFMergeMailStreamOrderRequest.cs", "ViewState": "AQIAAA4AAAAAAAAAAADwvxkAAAAFAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-08-24T17:27:02.958Z"}]}]}]}