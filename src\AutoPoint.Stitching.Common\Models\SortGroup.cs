﻿using System;

namespace AutoPoint.Stitching.Common.Models
{
    public class SortGroup : CifSplit
    {
        public int SortGroupWk { get; set; }
        public int SortGroupDefnWk { get; set; }
        public string SortGroupName { get; set; }
        public bool IncludeInkJet { get; set; }
        public bool IncludeStoreFrontDetails { get; set; }

    }

    public class SortGroupDto
    {
        public int sort_group_wk;

        public int sort_group_defn_wk;

        public string sort_group_name;

        public int? audit_wk;

        public string name;

        public int mailing;

        public string reference_id;

        public DateTime? due_date;

        public DateTime? start_date;

        public DateTime? end_date;

        public int start_record_count;

        public int? end_record_count;

        public int? error_record_count;

        public int? source_job_wk;

        public int? vdf_job_wk;

        public int? fulfillment_vendor_wk;

        public bool is_test_mode;

        public int communication_media_type_wk;

        public bool is_commingle;

        public bool include_inkjet;

        public bool include_storefront_details;

        public bool? is_ipn;
    }
}
