﻿using AutoPoint.Stitching.Common.Enums;

namespace AutoPoint.Stitching.Common.Models
{
    public class ProcessSplitRequest
    {

        public int SplitWk { get; set; }
        public int SplitDefnWk { get; set; }
        public string SplitDefnName { get; set; }
        public int SplitJobWk { get; set; }
        public int SplitTaskWk { get; set; }
        public int SourceJobWk { get; set; }
        public int xmpie_email_proof_wk { get; set; }
        public bool IsOnDemand { get; set; }
        public int? MarketNowProofWk { get; set; }
        public int CatalogOrderWk { get; set; }
        public int OnDemandCouponPosition { get; set; }
        public string OnDemandCouponDocId { get; set; }
        public bool GenerateDynamicReportSQL { get; set; }
        public int OnDemandCouponVersion { get; set; }
        public int OutputType { get; set; }
        public int RequestType { get; set; }

        public bool IsSample { get; set; }
        public bool IsPrint { get; set; }
        public bool IsPDFO { get; set; }
        public bool IsCommingle { get; set; }

        public int MaxRetry { get; set; }
        public int RetryCount { get; set; }
        public XmpieRequestType XmpieRequestType { get; set; }

        // Constructor
        public ProcessSplitRequest()
        {
            IsOnDemand = false;
            IsPDFO = false;
            GenerateDynamicReportSQL = false;
            MaxRetry = 1;
            RetryCount = 0;
        }
    }
}
