﻿using AutoPoint.Stitching.Common.Interfaces;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics.CodeAnalysis;
using System.Threading;
using System.Threading.Tasks;

namespace AutoPoint.Stitching.Common
{
    [ExcludeFromCodeCoverage]
    /// <summary>
    ///     Sql Extensions for Retry
    /// </summary>
    public static class RetrySqlExtensions
    {
        /// <summary>
        ///     Open with retry
        /// </summary>
        /// <param name="conn"></param>
        /// <param name="retryPolicy"></param>
        /// <param name="retryStrategy"></param>
        /// <returns></returns>
        public static SqlConnection OpenWithRetry(this SqlConnection conn, IRetryPolicy retryPolicy,
            RetryStrategy retryStrategy = null)
        {
            retryPolicy.Execute(conn.Open, retryStrategy);
            return conn;
        }

        /// <summary>
        ///     Open with retry async
        /// </summary>
        /// <param name="conn"></param>
        /// <param name="retryPolicy"></param>
        /// <param name="retryStrategy"></param>
        /// <returns></returns>
        public static async Task<SqlConnection> OpenWithRetryAsync(this SqlConnection conn, IRetryPolicy retryPolicy,
            RetryStrategy retryStrategy = null)
        {
            return await conn.OpenWithRetryAsync(CancellationToken.None, retryPolicy, retryStrategy);
        }

        /// <summary>
        ///     Open with retry async
        /// </summary>
        /// <param name="conn"></param>
        /// <param name="cancellationToken"></param>
        /// <param name="retryPolicy"></param>
        /// <param name="retryStrategy"></param>
        /// <returns></returns>
        public static async Task<SqlConnection> OpenWithRetryAsync(this SqlConnection conn,
            CancellationToken cancellationToken, IRetryPolicy retryPolicy, RetryStrategy retryStrategy = null)
        {
            await retryPolicy.ExecuteAsync(conn.OpenAsync, cancellationToken, retryStrategy);
            return conn;
        }

        /// <summary>
        /// Open with retry and return <typeparamref name="TResult"/>
        /// </summary>
        /// <typeparam name="TResult"></typeparam>
        /// <param name="conn"></param>
        /// <param name="process"></param>
        /// <param name="retryPolicy"></param>
        /// <param name="retryStrategy"></param>
        /// <returns></returns>
        public static TResult OpenWithRetry<TResult>(this SqlConnection conn, Func<SqlConnection, TResult> process, IRetryPolicy retryPolicy, RetryStrategy retryStrategy = null)
        {
            return retryPolicy.Execute(() =>
            {
                if (conn.State != ConnectionState.Open)
                    conn.Open();
                return process(conn);
            }, retryStrategy);
        }

        /// <summary>
        ///     Open with retry and return <typeparamref name="TResult" />
        /// </summary>
        /// <typeparam name="TResult"></typeparam>
        /// <param name="conn"></param>
        /// <param name="process"></param>
        /// <param name="retryPolicy"></param>
        /// <param name="retryStrategy"></param>
        /// <returns></returns>
        public static Task<TResult> OpenWithRetryAsync<TResult>(this SqlConnection conn,
            Func<SqlConnection, Task<TResult>> process, IRetryPolicy retryPolicy, RetryStrategy retryStrategy = null)
        {
            return conn.OpenWithRetryAsync(process, CancellationToken.None, retryPolicy, retryStrategy);
        }

        /// <summary>
        ///     Open with retry and return <typeparamref name="TResult" />
        /// </summary>
        /// <typeparam name="TResult"></typeparam>
        /// <param name="conn"></param>
        /// <param name="process"></param>
        /// <param name="cancellationToken"></param>
        /// <param name="retryPolicy"></param>
        /// <param name="retryStrategy"></param>
        /// <returns></returns>
        public static Task<TResult> OpenWithRetryAsync<TResult>(this SqlConnection conn,
            Func<SqlConnection, Task<TResult>> process, CancellationToken cancellationToken, IRetryPolicy retryPolicy,
            RetryStrategy retryStrategy = null)
        {
            return retryPolicy.ExecuteAsync(async ct =>
            {
                if (conn.State != ConnectionState.Open)
                    await conn.OpenAsync(ct);
                return await process(conn);
            }, cancellationToken, retryStrategy);
        }
    }
}
