﻿using System;
using System.Diagnostics;
using System.Reflection;
using System.Text;

namespace AutoPoint.Stitching.Common
{
    public class VdfException : ApplicationException
    {
        #region Properties

        private string m_formattedMessage;
        public string FormattedMessage
        {
            get
            {
                if (m_formattedMessage == null)
                {
                    StringBuilder sb = new StringBuilder(this.Message);
                    if (this.Data.Keys.Count > 0)
                    {
                        sb.Append(" ");
                        foreach (object item in this.Data.Keys)
                        {
                            sb.AppendFormat("[{0}: {1}]", item, this.Data[item]);
                        }
                    }
                    m_formattedMessage = sb.ToString();
                }
                return m_formattedMessage;
            }
        }

        #endregion Properties

        #region Constructors / Destructors

        public VdfException() : base() { }
        public VdfException(string message) : base(message) { }
        public VdfException(string message, Exception inner) : base(message, inner) { }

        #endregion Constructors / Destructors
    }

    public static class VdfExceptionExtension
    {
        public static void AddDataWithKeyCheck(this VdfException t, object key, object value)
        {
            if (!t.Data.Contains(key))
            {
                t.Data.Add(key, value);
            }
        }

        public static void AddObjectToData(this VdfException t, object data)
        {
            // No need to continue if object is invalid
            if (data == null) return; /* PRE-MATURE EXIT!!! */

            // Get public fields
            Type dataType = data.GetType();
            PropertyInfo[] properties = dataType.GetProperties(BindingFlags.Instance | BindingFlags.Public);
            for (int i = 0; i < properties.Length; i++)
            {
                PropertyInfo property = properties[i];
                try
                {
                    // TODO: Check ways to serialize objects with indexers
                    bool isList = (property.GetIndexParameters().Length > 0);
                    if (isList) continue;

                    // Add simple data types
                    if ((property.PropertyType.IsValueType && property.PropertyType.IsPrimitive) || (property.PropertyType == typeof(string)))
                    {
                        string key = String.Format("{0}.{1}", dataType.Name, property.Name);
                        object value = property.GetValue(data, null);
                        t.AddDataWithKeyCheck(key, value);
                    }
                }
                catch
                {
                    // TODO: This can be safely ignored but check this out when this happens!!!
                    Debugger.Break();
                }
            }
        }
    }
}
