﻿using System;
using System.Diagnostics.CodeAnalysis;

namespace AutoPoint.Stitching.Common
{
    [ExcludeFromCodeCoverage]
    [Serializable]
    public class RetryPolicyException : ApplicationException
    {

        public object State { get; private set; }
        public RetryPolicyException(string message, object state, Exception innerException)
            : base(message, innerException)
        {
            State = state;
        }
        public RetryPolicyException(string message, object state)
            : base(message)
        {
            State = state;
        }
    }
}
