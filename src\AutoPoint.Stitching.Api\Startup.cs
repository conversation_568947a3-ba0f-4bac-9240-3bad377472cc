﻿using Microsoft.OpenApi.Models;
using System.Diagnostics.CodeAnalysis;
using AutoPoint.Stitching.Api.LogProvider;
using Serilog;
using AutoPoint.Stitching.Api.Middleware;
using Newtonsoft.Json;
using AutoPoint.Stitching.Common;
using AutoPoint.Stitching.Common.Interfaces;
using AutoPoint.Stitching.Services;
using AutoPoint.Stitching.DataProviders;
using AutoPoint.Stitching.Common.Models;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;

namespace AutoPoint.Stitching.Api
{
    [ExcludeFromCodeCoverage]
    public class Startup
    {
        public IConfiguration Configuration { get; }
        public IHostEnvironment HostingEnvironment { get; }
        public Startup(IConfiguration configuration, IHostEnvironment env)
        {
            Configuration = configuration;

            // Setup SeriLog
            SeriLogSetup.ConfigureLogger(configuration);

            HostingEnvironment = env;
        }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            // Register the Serilog logger
            services.AddSingleton(Log.Logger);
            services.AddLogging(loggingBuilder => loggingBuilder.AddSerilog(dispose: true));
            ConfigurationHelper.Initialize(Configuration);
            services.AddSingleton<IStitchingConfig, StitchingConfig>();
            services.AddHealthChecks();
            services.AddRetryPolicy(ServiceLifetime.Singleton);

            services.AddControllers();
            services.AddMvc().AddNewtonsoftJson(options => options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore);
            services.AddMvc().SetCompatibilityVersion(CompatibilityVersion.Version_3_0);

            services.AddSwaggerGen(options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo
                {
                    Title = "AutoPoint Stitching API",
                    Version = "v1",
                    Description = "AutoPoint Stitching API.",
                });

                options.AddSecurityDefinition("x-api-key", new OpenApiSecurityScheme() { Type = SecuritySchemeType.ApiKey, Name = "x-api-key", In = ParameterLocation.Header });
                options.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme()
                        {
                            Reference = new OpenApiReference()
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "x-api-key"
                            },
                            Type = SecuritySchemeType.ApiKey,
                            Name = "x-api-key",
                            In = ParameterLocation.Header
                        },
                        new List<string>()
                    }
                });
            });


            services.AddTransient<IVdfProvider, VdfProvider>();
            services.AddTransient<IStitchingService, StitchingService>();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, ILoggerFactory loggerFactory)
        {
            // Add Serilog to the logging pipeline
            app.UseSerilogRequestLogging();
            app.UseHealth();
            app.UseSwagger();
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "AutoPoint Stitching API");
            });
            app.UseBasicAuth();
            app.UseGlobalExceptionHandling();
            app.UseCors(cors => cors.AllowAnyOrigin());
            app.UseRouting();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
