﻿using AutoPoint.Stitching.Common.Models;
using System.Collections.Generic;

namespace AutoPoint.Stitching.Common.Interfaces
{
    public interface IStitchingService
    {
        bool MergeAndSavePdfDocument(PDFMergeRequest request);
        bool MergePDFDocuments(PDFMergeRequest request);
        int GetPDFFileCount(string inputFile);
        bool MergePDFDocumentsInMailstreamOrder(XmpieJobWorkItem workItem, string[] inputFiles, string outputFile, bool deleteOriginals, bool sourceLocationOverride,
            string overrideLocation, int? mergeBatchsize, bool commingleOnly,
            bool deleteInputFiles, string docId, string fileAppend, List<string> deleteFiles, int pagesPerContact, int taskTrackingRequestWk);
        bool MergePDFDocumentsForODP(PDFMergeODPRequest request);
        bool MergeMergeFiles(MergeMergeFilesRequest request);
        bool MarketNowProofPDFSplit(MarketNowProofPDFSplitRequest request);
        bool GenerateImageContactSheetProof(GenerateImageContactSheetProofRequest request);
        bool GenerateContactSheetV2(GenerateContactSheetV2Request request);
        bool ProcessRotateProofPages(RotatePagesRequest request);
    }
}
