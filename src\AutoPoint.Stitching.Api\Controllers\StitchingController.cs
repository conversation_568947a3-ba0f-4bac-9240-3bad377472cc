using AutoPoint.Stitching.Common.Interfaces;
using AutoPoint.Stitching.Common.Models;
using AutoPoint.Stitching.Services;
using Microsoft.AspNetCore.Mvc;

namespace AutoPoint.Stitching.Api.Controllers
{
    [ApiController]
    [Route("api/pdf")]
    public class StitchingController : ControllerBase
    {
        private readonly IStitchingService _stitchingService;

        public StitchingController(IStitchingService stitchingService)
        {
            _stitchingService = stitchingService;
        }

        [HttpPost, Route("MergeAndSavePdf")]
        public bool MergePDFs([FromBody] PDFMergeRequest request)
        {
            return _stitchingService.MergeAndSavePdfDocument(request);
        }

        [HttpPost, Route("MergePDFDocumentsInMailstreamOrder")]
        public bool MergePDFDocumentsInMailstreamOrder([FromBody] PDFMergeMailStreamOrderRequest request)
        {
            return _stitchingService.MergePDFDocumentsInMailstreamOrder(
                request.WorkItem, 
                request.InputFiles, 
                request.OutputFile, 
                request.DeleteOriginals, 
                request.SourceLocationOverride, 
                request.OverrideLocation, 
                request.MergeBatchsize, 
                request.CommingleOnly, 
                request.DeleteInputFiles, 
                request.DocId, 
                request.FileAppend, 
                request.DeleteFiles, 
                request.PerContactPages,
                request.TaskAbortRequestWk ?? -1);
        }

        [HttpPost, Route("MergePdfDocuments")]
        public bool MergePDFDocuments([FromBody] PDFMergeRequest request)
        {
            return _stitchingService.MergePDFDocuments(request);
        }

        [HttpPost, Route("MergePDFDocumentsForODP")]
        public bool MergePDFDocumentsForODP([FromBody] PDFMergeODPRequest request)
        {
            return _stitchingService.MergePDFDocumentsForODP(request);
        }

        [HttpPost, Route("MergeMergeFiles")]
        public bool MergeMergeFiles([FromBody] MergeMergeFilesRequest request)
        {
            return _stitchingService.MergeMergeFiles(request);
        }

        [HttpPost, Route("MarketNowProofPDFSplit")]
        public bool MarketNowProofPDFSplit([FromBody] MarketNowProofPDFSplitRequest request)
        {
            return _stitchingService.MarketNowProofPDFSplit(request);
        }

        [HttpPost, Route("GenerateImageContactSheetProof")]
        public bool GenerateImageContactSheetProof([FromBody] GenerateImageContactSheetProofRequest request)
        {
            return _stitchingService.GenerateImageContactSheetProof(request);
        }

        [HttpPost, Route("GenerateContactSheetV2")]
        public bool GenerateContactSheetV2([FromBody] GenerateContactSheetV2Request request)
        {
            return _stitchingService.GenerateContactSheetV2(request);
        }

        [HttpPost, Route("RotateProofPages")]
        public bool RotateProofPagesRequest([FromBody] RotatePagesRequest request)
        {
            return _stitchingService.ProcessRotateProofPages(request);
        }

        [HttpGet, Route("PdfFileCount")]
        public int GetPdfFileCount(string pdfFile)
        {
            return _stitchingService.GetPDFFileCount(pdfFile);
        }
    }
}
