﻿namespace AutoPoint.Stitching.Common.Enums
{
    public enum XmpieRequestType
    {
        None = 0,
        Print = 1,
        SamplePrint = 2,
        Proof = 3,
        Email = 4,
        PrintCoupon = 5,
        DigitalCoupon = 6,
        DigitalCouponForDirectMail = 7,
        RecipientDigital = 8,
        RecipientDigitalPrint = 9,
        PrintCouponContactSheet = 10,
        DigitalCouponImagePreview = 11,
        OnDemandProof = 12,
        OnDemandPrint = 13,
        OnDemandSamplePrint = 14,
        OnDemandEmailProof = 15,
        OnDemandDigitalCoupon = 16,
        PrintPDFO = 17,
        OnDemandCatalogOrderProof = 18,
        OnDemandCatalogOrderEmailProof = 19,
        OnDemandDigitalCatalogOrderCoupon = 20,
        DigitalDisplayAdProof = 21,
        DigitalDisplayAdPrint = 22,
        PrintCouponThemeProof = 23,
        OnDemandDigitalCatalogOrderCouponV2 = 24,
        OnDemandPrintCatalogOrderCouponV2 = 25,
        OnDemandCollateralPrint = 26,
        OnDemandCollateralDigital = 27,
        OnDemandDigitalDisplayAdPrint = 28,
        OnDemandDigitalDisplayAdLanding = 29,
        OnDemandDigitalCatalogOrderImageOverlay = 30,
        OnDemandPrintCatalogOrderImageOverlay = 31
    }
}
