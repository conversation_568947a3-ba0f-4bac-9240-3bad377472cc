{"Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Debug", "System": "Debug"}}, "Properties": {"ApplicationName": "AutoPoint Stitching API Local"}}, "Auth": {"Realm": "AutoPoint", "api-x-key": "jZkHjC5eAV9CR10SHhHcTWX8aUqrkNrHHN8jTwIyUT0r2sevhmnhxm53AsHQAlYU"}, "ConnectionStrings": {"RRS_VDF": "server=SQL_VDF;database=RRS_VDF;Integrated Security=True;", "RRS_JobManager": "server=SQL_JM;database=RRS_JobManager;Integrated Security=True;", "RRS_Logging": "server=SQL_VDF;database=RRS_Logging;Integrated Security=True;", "RRS_NCOA": "server=svrrrdb15;database=RRS_NCOA;Integrated Security=True;"}, "StitchingSettings": {"DefaultBatchSize": 1250, "DefaultPagesPerContact": 2, "OnDemandFileStore_PDFO": "\\\\svrrrint03\\bdc_2007\\shipping\\push\\variable_print", "OnDemandFileStore_PDFVT": "\\\\svrrrint03\\bdc_2007\\shipping\\push\\variable_print\\release\\@vdf_job_wk", "OnDemandStitchDeleteOriginals": true, "OnDemandFileStore_CampaignProofs": "\\\\svrrrOnDemandAssets\\OnDemand_Proofs\\@catalog_order_wk", "MergeFileContacts": 50, "MergeFileSaveThreads": 1, "OnDemandFile_OutPutType": "PDFVT", "OnDemandFileStore_Local": "E:\\ondemand\\@vdf_job_wk", "OnDemandStitchDiskSpaceInMB": 3400, "OnDemandStitchCPUThresholdPercent": 70, "OnDemandStitchRAMThresholdPercent": 70, "OnDemandStitchJobsThreshold": 5, "SingeOrderCIFStitchingThreshold": 40000, "MergedCompleteFilename": "{0}_Final_{1}.pdf", "NewRelicAgentEnabled": true, "NewRelicAppName": "CRM-DMEA-STITCHING-PROVIDER-API-DEV", "NewRelicLicenseKey": "aabae9bf3bb6d876ad6df27363d5e892e7323c6a", "PDFTronKey": "Mobile Productivity  LLC  Formerly Solera AutoPoint :ENTCPU:1::W:AMS(20260928):277FEA933CFF26B9428E4007400DD2FCE2AB025BF4547018CE62CABEF5C7", "ContactSheetHeaderFilePath": "\\\\svrrrint03\\bdc_2007\\Misc\\contact_sheet_header.pdf", "VerboseLogging": false, "OnDemandFileStore_UNC": "\\\\duswdmdvapPDF01\\ondemand\\@vdf_job_wk"}, "AllowedHosts": "*", "Elasticsearch": {"Nodes": "https://dev-kibana80.redrocketsolutions.com", "UserName": "dmea_app_log", "Password": "FEED-bell-between-burn", "IndexFormat": "crm_dmea_applog-{0:yyyy.MM.dd}"}}