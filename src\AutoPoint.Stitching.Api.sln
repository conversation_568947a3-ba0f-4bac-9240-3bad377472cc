﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34330.188
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoPoint.Stitching.Api", "AutoPoint.Stitching.Api\AutoPoint.Stitching.Api.csproj", "{B873F5B7-9DD4-473F-88D2-6583F05B4655}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoPoint.Stitching.Common", "AutoPoint.Stitching.Common\AutoPoint.Stitching.Common.csproj", "{59BB6CF8-C91C-4F3F-85EE-6C827504F496}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoPoint.Stitching.DataProviders", "AutoPoint.Stitching.DataProviders\AutoPoint.Stitching.DataProviders.csproj", "{66685D6D-4897-4B05-B172-C8FEB56801CF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoPoint.Stitching.Services", "AutoPoint.Stitching.Services\AutoPoint.Stitching.Services.csproj", "{5D380C07-A0FA-4BD1-A1A0-9CD99BE06D76}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Development|Any CPU = Development|Any CPU
		Production|Any CPU = Production|Any CPU
		Release|Any CPU = Release|Any CPU
		Staging|Any CPU = Staging|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B873F5B7-9DD4-473F-88D2-6583F05B4655}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B873F5B7-9DD4-473F-88D2-6583F05B4655}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B873F5B7-9DD4-473F-88D2-6583F05B4655}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{B873F5B7-9DD4-473F-88D2-6583F05B4655}.Development|Any CPU.Build.0 = Development|Any CPU
		{B873F5B7-9DD4-473F-88D2-6583F05B4655}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{B873F5B7-9DD4-473F-88D2-6583F05B4655}.Production|Any CPU.Build.0 = Production|Any CPU
		{B873F5B7-9DD4-473F-88D2-6583F05B4655}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B873F5B7-9DD4-473F-88D2-6583F05B4655}.Release|Any CPU.Build.0 = Release|Any CPU
		{B873F5B7-9DD4-473F-88D2-6583F05B4655}.Staging|Any CPU.ActiveCfg = Staging|Any CPU
		{B873F5B7-9DD4-473F-88D2-6583F05B4655}.Staging|Any CPU.Build.0 = Staging|Any CPU
		{59BB6CF8-C91C-4F3F-85EE-6C827504F496}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{59BB6CF8-C91C-4F3F-85EE-6C827504F496}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{59BB6CF8-C91C-4F3F-85EE-6C827504F496}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{59BB6CF8-C91C-4F3F-85EE-6C827504F496}.Development|Any CPU.Build.0 = Development|Any CPU
		{59BB6CF8-C91C-4F3F-85EE-6C827504F496}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{59BB6CF8-C91C-4F3F-85EE-6C827504F496}.Production|Any CPU.Build.0 = Production|Any CPU
		{59BB6CF8-C91C-4F3F-85EE-6C827504F496}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{59BB6CF8-C91C-4F3F-85EE-6C827504F496}.Release|Any CPU.Build.0 = Release|Any CPU
		{59BB6CF8-C91C-4F3F-85EE-6C827504F496}.Staging|Any CPU.ActiveCfg = Staging|Any CPU
		{59BB6CF8-C91C-4F3F-85EE-6C827504F496}.Staging|Any CPU.Build.0 = Staging|Any CPU
		{66685D6D-4897-4B05-B172-C8FEB56801CF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{66685D6D-4897-4B05-B172-C8FEB56801CF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{66685D6D-4897-4B05-B172-C8FEB56801CF}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{66685D6D-4897-4B05-B172-C8FEB56801CF}.Development|Any CPU.Build.0 = Development|Any CPU
		{66685D6D-4897-4B05-B172-C8FEB56801CF}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{66685D6D-4897-4B05-B172-C8FEB56801CF}.Production|Any CPU.Build.0 = Production|Any CPU
		{66685D6D-4897-4B05-B172-C8FEB56801CF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{66685D6D-4897-4B05-B172-C8FEB56801CF}.Release|Any CPU.Build.0 = Release|Any CPU
		{66685D6D-4897-4B05-B172-C8FEB56801CF}.Staging|Any CPU.ActiveCfg = Staging|Any CPU
		{66685D6D-4897-4B05-B172-C8FEB56801CF}.Staging|Any CPU.Build.0 = Staging|Any CPU
		{5D380C07-A0FA-4BD1-A1A0-9CD99BE06D76}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5D380C07-A0FA-4BD1-A1A0-9CD99BE06D76}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5D380C07-A0FA-4BD1-A1A0-9CD99BE06D76}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{5D380C07-A0FA-4BD1-A1A0-9CD99BE06D76}.Development|Any CPU.Build.0 = Development|Any CPU
		{5D380C07-A0FA-4BD1-A1A0-9CD99BE06D76}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{5D380C07-A0FA-4BD1-A1A0-9CD99BE06D76}.Production|Any CPU.Build.0 = Production|Any CPU
		{5D380C07-A0FA-4BD1-A1A0-9CD99BE06D76}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5D380C07-A0FA-4BD1-A1A0-9CD99BE06D76}.Release|Any CPU.Build.0 = Release|Any CPU
		{5D380C07-A0FA-4BD1-A1A0-9CD99BE06D76}.Staging|Any CPU.ActiveCfg = Staging|Any CPU
		{5D380C07-A0FA-4BD1-A1A0-9CD99BE06D76}.Staging|Any CPU.Build.0 = Staging|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {2A530A2A-EA0E-43B8-8586-2BB73D883082}
	EndGlobalSection
EndGlobal
