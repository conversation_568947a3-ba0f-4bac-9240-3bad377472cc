﻿namespace AutoPoint.Stitching.Common.Models
{
    public class XmpieJobToOutputPath
    {
        public string DocumentId { get; set; }
        public string[] JobIds { get; set; }
        public string[] OutputPath { get; set; }
        public string OutputType { get; set; }
        public string OnDemandFilename { get; set; }
        public string MarketingStrategyName { get; set; }
        public string OrgNk { get; set; }
        public int OrgWk { get; set; }
        public int CatalogOrderWk { get; set; }
        public int IPNShipToTypeWk { get; set; }
        public int IPNShippingMethodWk { get; set; }
        public int XmpieJobDefnWk { get; set; }
        public int SamplingRuleWk { get; set; }
        public bool IsCoopSample { get; set; }
        public bool IsCommingle { get; set; }
        public bool IsPrimaryDocument { get; set; }
        public int CouponWk { get; set; }
        public string EmailProviderId { get; set; }
    }
}
