﻿using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using System.Threading;
using AutoPoint.Stitching.Common.Interfaces;

namespace AutoPoint.Stitching.Common
{
    public class DbRetryPolicy : IRetryPolicy
    {
        private readonly ILogger<DbRetryPolicy> _logger;

        public DbRetryPolicy(ILogger<DbRetryPolicy> logger)
        {
            _logger = logger;
        }

        public RetryPolicyType RetryPolicyType { get; } = RetryPolicyType.SqlDbConnection;

        public void Execute(Action operation, RetryStrategy retryStrategy = null)
        {
            GetRetryPolicy(retryStrategy).Execute(operation.Invoke);
        }

        public Task<TResult> Execute<TResult>(Func<Task<TResult>> operation, RetryStrategy retryStrategy = null)
        {
            return GetRetryPolicy(retryStrategy).Execute(() => operation.Invoke());
        }

        public Task ExecuteAsync(Func<CancellationToken, Task> operation, CancellationToken cancellationToken, RetryStrategy retryStrategy = null)
        {
            return GetRetryPolicy(retryStrategy).ExecuteAsync(ct => operation.Invoke(ct), cancellationToken);
        }

        public async Task<TResult> ExecuteAsync<TResult>(Func<CancellationToken, Task<TResult>> operation, CancellationToken cancellationToken, RetryStrategy retryStrategy = null)
        {
            return await GetRetryPolicy(retryStrategy).ExecuteAsync(ct => operation.Invoke(ct));

        }

        private RetryPolicy GetRetryPolicy(RetryStrategy retryStrategy = null)
        {

            retryStrategy ??= new RetryStrategy();

            return new RetryPolicy(_logger, retryStrategy.RetryCount, retryStrategy.WaitBetweenRetries);

        }

        public TResult Execute<TResult>(Func<TResult> operation, RetryStrategy retryStrategy = null)
        {
            return GetRetryPolicy(retryStrategy).Execute(operation.Invoke);
        }
    }
}
