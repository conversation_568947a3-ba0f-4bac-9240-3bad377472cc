﻿using Serilog.Sinks.Elasticsearch;
using Serilog;
using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;

namespace AutoPoint.Stitching.Api.LogProvider
{
    [ExcludeFromCodeCoverage]
    public static class SeriLogSetup
    {
        public static void ConfigureLogger(IConfiguration Configuration)
        {
            var envVariables = Environment.GetEnvironmentVariables();
            var elasticSearchNodes = Configuration["Elasticsearch:Nodes"];
            var elasticSearchUserName = Configuration["Elasticsearch:UserName"];
            var elasticSearchPassword = Configuration["Elasticsearch:Password"];
            var elasticSearchIndexFormat = Configuration["Elasticsearch:IndexFormat"] ?? "crm_dmea_applog-{0:yyyy.MM.dd}";
            var nodes = new List<Uri>();
            elasticSearchNodes.Split(',').ToList().ForEach(n => { nodes.Add(new Uri(n)); });

            Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .Enrich.FromLogContext()
            .WriteTo.Elasticsearch(new ElasticsearchSinkOptions(nodes)
            {
                AutoRegisterTemplate = true,
                AutoRegisterTemplateVersion = AutoRegisterTemplateVersion.ESv7,
                IndexFormat = elasticSearchIndexFormat,
                ModifyConnectionSettings = config => config
                    .BasicAuthentication(elasticSearchUserName, elasticSearchPassword)
                    .ConnectionLimit(-1)
                    .ServerCertificateValidationCallback((o, certificate, arg3, arg4) => { return true; })
            })
            .WriteTo.Console()
            .Enrich.WithEnvironmentUserName()
            .Enrich.WithMachineName()
            .Enrich.WithProcessId()
            .Enrich.WithProcessName()
            .Enrich.WithAssemblyName()
            .Enrich.WithAssemblyVersion()
            .Enrich.WithProperty("ApplicationName", Configuration["Serilog:Properties:ApplicationName"])
            .CreateLogger();
        }
    }
}
