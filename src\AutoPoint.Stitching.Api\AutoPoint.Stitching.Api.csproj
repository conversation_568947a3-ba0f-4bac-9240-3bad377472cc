﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
	  <AspNetCoreHostingModel>inprocess</AspNetCoreHostingModel>
	  <Configurations>Debug;Release;Development;Staging;Qa;Production</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.32" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog.AspNetCore" Version="3.4.0" />
    <PackageReference Include="Serilog.Enrichers.AssemblyName" Version="1.0.9" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="2.3.0" />
    <PackageReference Include="Serilog.Enrichers.Process" Version="2.0.2" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="3.1.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="8.4.1" />
    <PackageReference Include="Serilog.Sinks.EventLog" Version="3.1.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="5.6.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AutoPoint.Stitching.Common\AutoPoint.Stitching.Common.csproj" />
    <ProjectReference Include="..\AutoPoint.Stitching.DataProviders\AutoPoint.Stitching.DataProviders.csproj" />
    <ProjectReference Include="..\AutoPoint.Stitching.Services\AutoPoint.Stitching.Services.csproj" />
  </ItemGroup>

</Project>
