﻿using Serilog;
using System.Diagnostics.CodeAnalysis;
using System.Security.Principal;
using AutoPoint.Stitching.Api.Auth;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Http;
using System.Linq;
using System.Threading.Tasks;

namespace AutoPoint.Stitching.Api.Middleware
{
    [ExcludeFromCodeCoverage]
    public class BasicAuthMiddleWare
    {
        private readonly RequestDelegate _next;
        private readonly IConfiguration _configuration;

        public BasicAuthMiddleWare(RequestDelegate next, IConfiguration configuration)
        {
            _next = next;
            _configuration = configuration;
        }

        // Purpose: Basic Authentication 
        public async Task Invoke(HttpContext context, IConfiguration config)
        {
            var authorized = context.User?.Identity?.IsAuthenticated ?? false;
            var headerApiKey = context.Request.Headers["x-api-key"].FirstOrDefault();

            if (!authorized && !string.IsNullOrWhiteSpace(headerApiKey))
            {
                var apiKey = config["Auth:api-x-key"];

                if (string.Equals(apiKey, headerApiKey))
                {
                    var identity = new CustomIdentity("apuser", "Basic");
                    context.User = new GenericPrincipal(identity, null);
                    authorized = true;
                    Log.Information(string.Format("Authorized => {1} [{0}]", "apuser", context.Connection.RemoteIpAddress));
                }
                else
                {
                    Log.Warning(string.Format("Unauthorized => {1} [ApiKey {0}]", apiKey, context.Connection.RemoteIpAddress));
                }
            }

            if (!authorized)
            {
                string realm = config["Auth:Realm"];
                UnauthorizedResponse(context, realm);

                return;
            }

            await _next(context);
        }

        private void UnauthorizedResponse(HttpContext context, string realm)
        {
            context.Response.StatusCode = 401;
            context.Response.Headers.Add("WWW-Authenticate", string.Format("Basic realm=\"{0}\"", realm));
        }
    }
}
