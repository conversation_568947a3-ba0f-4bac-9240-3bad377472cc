﻿using System.Collections.Generic;
using AutoPoint.Stitching.Common.Enums;

namespace AutoPoint.Stitching.Common.Models
{
    public class XmpieJobWorkItem
    {
        public int? CifSplitWk { get; set; }
        public int? CifSplitDefnWk { get; set; }
        public string UserName { get; set; }
        public string Password { get; set; }
        public string ConnectionString { get; set; }
        public string ConnectionType { get; set; }
        public int MediaType { get; set; }
        public int RecipientFilterType { get; set; }
        public int Timeout { get; set; }
        public int SourceJobWk { get; set; }
        public int VdfJobWk { get; set; }
        // DME-5002: Alter ProcessCopy() method to copy PPML files to final release folder
        // Needed a known placeholder for the vdf_job_wk
        //public int JMJobID { get; set; }

        public XmpieRequestType RequestType { get; set; }

        public string FilePath { get; set; }

        public string JobManagerNsbMessageSoaUrl { get; set; }
        public string JobManagerNsbServiceConfig { get; set; }
        public string PrintTaskDefnName { get; set; }
        public string ProofTaskDefnName { get; set; }
        public string EmailTaskDefnName { get; set; }
        public string SamplePrintTaskDefnName { get; set; }
        public string DigitalTaskDefnName { get; set; }
        public string CouponPrintTaskDefnName { get; set; }
        public string XmpieEmailOverRide { get; set; }
        public string XmpiePreProofEmail { get; set; }
        public int xmpie_email_proof_wk { get; set; }
        public int RetryAttempts { get; set; }
        public CifSplitJob splitJob { get; set; }
        public VDFCampaignVersion VersionType { get; set; }
        public bool? IncludeCouponContactSheet { get; set; }
        public InvokedMethods MethodToCall { get; set; }
        public XmpieOutputType OutPutType { get; set; }
        public int JobStatusCheckRetryCount { get; set; }
        public int JobStatusCheckResetCount { get; set; }
        public int JobStatusSleepInSecond { get; set; }
        public int CifTaskWk { get; set; }
        public XmpieJobDefnMethods JobDefnMethod { get; set; }
        public XmpieRotatePages RotatePages { get; set; }
        public int Rotation { get; set; }
        public bool? IsODPJob { get; set; }
        public bool? IsOnDemand { get; set; }
        public string OnDemandJobName { get; set; }
        public List<XmpieJobToOutputPath> XmpieJobOutputList { get; set; }
        public List<JobFiles> jobFiles { get; set; }
        public ProcessOnDemandEmailCouponRequest OnDemandDigitalCouponByPositionRequest { get; set; }
        public bool? IsDemandDigitalCouponByPositionRequest { get; set; }
        public bool IsSample { get; set; }
        public bool IsPrint { get; set; }
        public bool IsTestMode { get; set; }
        public XmpieJobWorkItem()
        {
            this.XmpieJobOutputList = new List<XmpieJobToOutputPath>();
            this.OnDemandDigitalCouponByPositionRequest = new ProcessOnDemandEmailCouponRequest();
        }
        public bool DealerVersion { get; set; }
        public string MarketingStrategySetName { get; set; }
        public bool IsSampleJob { get; set; }
        public bool IsPDFOPrintJob { get; set; }
        public int? MarketNowProofWk { get; set; }
        public int CatalogOrderWk { get; set; }
        public int OrgWk { get; set; }
        public int OnDemandCouponPosition { get; set; }
        public string OnDemandCouponDocId { get; set; }
        public int OnDemandCouponVersion { get; set; }
        public long DigitalCouponGenerateRequestWk { get; set; }
        public int CouponWk { get; set; }
    }
}
