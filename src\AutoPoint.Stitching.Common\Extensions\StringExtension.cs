﻿using System;
using System.Text;

namespace AutoPoint.Stitching.Common.Extensions
{
    public static class StringExtension
    {
        public static string ToLogString(this string[] t, char delimiter = ';')
        {
            // Initialize friendly job manager log string
            string s = String.Empty;

            // Setup friendly job manager log string
            if (t.Length > 0)
            {
                StringBuilder sb = new StringBuilder();
                foreach (string item in t)
                {
                    if (sb.Length > 0)
                    {
                        sb.Append(delimiter + " ");
                    }
                    sb.Append(item);
                }
                s = sb.ToString();
            }

            // Return friendly job manager log string
            return s;
        }
    }
}
