﻿namespace AutoPoint.Stitching.Common.Enums
{
    public enum InvokedMethods
    {
        SubmitDigitalCouponXmpieJobRequest = 1,
        SubmitPrintCouponXmpieJobRequest,
        SubmitPrintXmpieJobRequest,
        SubmitPrintXmpieJobRequestSampleSegment,
        SubmitProofXmpieJobRequest,
        SubmitCoopSampleXmpieJobRequest,
        SubmitCouponContactSheetXmpieJobRequest,
        SubmitEmailXmpieJobRequest,
        SubmitOnDemandXMPieProofRequest,
        SubmitOnDemandXMPiePrintRequest,
        SubmitOnDemandPrintXmpieJobRequestSampleSegment,
        SubmitOnDemandEmailXmpieJobRequest,
        SubmitOnDemandEmailCouponByPositionRequest,
        SubmitOnDemandEmailCouponCatalogOrderByPositionJobDefinition,
        SubmitOnDemandCatalogOrderProofJobDefinition,
        SubmitOnDemandCatalogOrderEmailProofJobDefinition,

        SubmitDDAProofXmpieJobRequest,
        SubmitDDAPrintXmpieJobRequest,
        SubmitCouponThemeProofXmpieJobRequest,
        SubmitOnDemandEmailCouponCatalogOrderJobDefinition,
        SubmitOnDemandPrintCouponCatalogOrderJobDefinition,
        SubmitOnDemandXMPieCollateralDigitalRequest,
        SubmitOnDemandXmpieRequest,
        SubmitOnDemandEmailImageOverlayCatalogOrderJobDefinition,
        SubmitOnDemandPrintImageOverlayCatalogOrderJobDefinition
    }
}
