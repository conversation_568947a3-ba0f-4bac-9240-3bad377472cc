﻿using System.Diagnostics.CodeAnalysis;
using System.Net;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using AutoPoint.Stitching.Common.Models;

namespace AutoPoint.Stitching.Api.Middleware
{
    [ExcludeFromCodeCoverage]
    public static class HealthMiddlewareExtensions
    {
        public static void UseHealth(this IApplicationBuilder app)
        {
            app.Map("/health", HandleHealth);
        }

        private static void HandleHealth(IApplicationBuilder app)
        {
            app.Run(async context =>
            {
                context.Response.ContentType = "application/json";
                context.Response.StatusCode = (int)HttpStatusCode.OK;
                await context.Response.WriteAsync(JsonConvert.SerializeObject(new ApiResponse
                {
                    IsSuccess = true,
                    Message = "Healthy"
                }));
            });
        }
    }
}
