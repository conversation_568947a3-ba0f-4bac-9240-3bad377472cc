﻿using AutoPoint.Stitching.Common.Interfaces;
using pdftron.Common;
using pdftron;
using AutoPoint.Stitching.Common.Models;
using PDF = pdftron.PDF;
using AutoPoint.Stitching.Common.Logger;
using AutoPoint.Stitching.Contracts.Enums;
using AutoPoint.Stitching.Common.Extensions;
using AutoPoint.Stitching.Common.Enums;
using Newtonsoft.Json;
using System.Collections;
using System.Diagnostics;
using pdftron.PDF;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using System.IO;
using System;
using System.Threading;
using System.Linq;
using AutoPoint.Stitching.Common;
using File = System.IO.File;

namespace AutoPoint.Stitching.Services
{
    public class StitchingService : IStitchingService
    {
        private readonly Serilog.ILogger _logger;
        private readonly IStitchingConfig _config;
        private readonly IVdfProvider _vdfProvider;
        public StitchingService(Serilog.ILogger logger, 
            IStitchingConfig config,
            IVdfProvider vdfProvider)
        {
            _logger = logger;
            _config = config;
            _vdfProvider = vdfProvider;
        }

        public bool MergeAndSavePdfDocument(PDFMergeRequest request)
        {
            try
            {
                // Log what we're doing...
                DMELoggerExtension.LogInformation(string.Format("Begin MergeAndSavePdfDocument - {0}", JsonConvert.SerializeObject(request)), DMELogType.Informtation, DMELogPriority.High, request.SplitTaskWk);

                var returnValue = false;

                PDFNet.Initialize(_config.PDFTronKey);
                PDF.PDFDoc in_doc = null;
                var merged_doc = new PDF.PDFDoc();

                try
                {
                    _logger.Information($"MergeAndSavePdfDocument process started. Input File Count: {request.InputFiles.Count()}. Output Path: {request.OutputPath}");
                    DMELoggerExtension.LogInformation($"MergeAndSavePdfDocument process started. Input File Count: {request.InputFiles.Count()}. Output Path: {request.OutputPath}", DMELogType.Informtation, DMELogPriority.High, request.TaskWk);

                    var pageCount = 1;
                    var startPage = 1;

                    foreach (var inputFile in request.InputFiles)
                    {
                        // Open the file...
                        in_doc = new PDF.PDFDoc(inputFile);

                        // Get the page count of the input document...
                        var cFilePages = in_doc.GetPageCount();

                        merged_doc.InsertPages
                        (
                            // ...the page to insert after...
                            pageCount,

                            // ...the input document...
                            in_doc,

                            // ...start page in input document...
                            startPage,

                            // ...number of pages in input document...
                            cFilePages,

                            // ...flags
                            PDF.PDFDoc.InsertFlag.e_none
                        );

                        pageCount = merged_doc.GetPageCount();

                        // Close our input file...Get the next file...
                        in_doc.Close();
                        in_doc.Dispose();
                        pageCount++;
                    }

                    // save to disk
                    merged_doc.Save(request.OutputPath, pdftron.SDF.SDFDoc.SaveOptions.e_compatibility);

                    merged_doc.Close();
                    merged_doc.Dispose();

                    _logger.Information($"MergeAndSavePdfDocument process completed. Input File Count: {request.InputFiles.Count()}. Output Path: {request.OutputPath}");
                    DMELoggerExtension.LogInformation($"MergeAndSavePdfDocument process completed. Input File Count: {request.InputFiles.Count()}. Output Path: {request.OutputPath}", DMELogType.Informtation, DMELogPriority.High, request.TaskWk);

                    returnValue = true;
                }
                catch (PDFNetException ex)
                {
                    _logger.Error(ex, $"MergeAndSavePdfDocument failed with {ex.Message}");
                    DMELoggerExtension.LogInformation(ex.Message, DMELogType.Exception, DMELogPriority.High, request.TaskWk);
                    throw new Exception(ex.Message);
                }
                finally
                {
                    if (merged_doc != null)
                    {
                        merged_doc.Close();
                        merged_doc.Dispose();
                    }

                    if (in_doc != null)
                    {
                        in_doc.Close();
                        in_doc.Dispose();
                    }
                }
                return returnValue;
            }
            catch (Exception ex)
            {
                DMELoggerExtension.LogInformation(string.Format("Error MergeAndSavePdfDocument - {0}", ex.Message), DMELogType.Exception, DMELogPriority.High, request.SplitTaskWk);
                throw;
            }
        }


        /// <summary>
		/// Purpose: Merge several pdf documents together.
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public bool MergePDFDocuments(PDFMergeRequest request)
        {
            try
            {
            // Log what we're doing...
            DMELoggerExtension.LogInformation(string.Format("Begin MergePDFDocuments - {0}", JsonConvert.SerializeObject(request)), DMELogType.Informtation, DMELogPriority.High, request.SplitTaskWk);

            int batchSize = request.SpooSize ?? _config.DefaultBatchSize;
            int pagesPerContact = request.PerContactPages ?? _config.DefaultPagesPerContact;

            DMELoggerExtension.LogInformation
                (
                    string.Format("MergePDFDocuments - spoolSize: {0}, perContactPages: {1}", request.SpooSize ?? -1, request.PerContactPages ?? -1),
                        DMELogType.Informtation,
                        DMELogPriority.High,
                        request.SplitTaskWk
                );

            bool retVal = false;
            string PDFTronKey = _config.PDFTronKey;
            PDFNet.Initialize(PDFTronKey);

            try
            {
                PDF.PDFDoc merged_doc = null;
                PDF.PDFDoc in_doc = null;

                int spoolSize = (batchSize * pagesPerContact);
                int startPage = 1;
                int pageCount = 0;
                int doc_num = 0;
                int fileCount = 0;

                bool requireNewFile = true;

                // Work through the list of files to merge...
                foreach (string inputFile in request.InputFiles)
                {
                    fileCount++;
                    startPage = 1;
                    string numberedOutput = string.Format("{0}_{1}.pdf", request.OutputPath, doc_num + 1);

                    // Open the file...
                    in_doc = new PDF.PDFDoc(inputFile);

                    // Log what we're doing...
                    DMELoggerExtension.LogInformation
                        (
                            string.Format("Merging file: {0} to {1}.  # Pages: {2}", inputFile, numberedOutput, in_doc.GetPageCount()),
                            DMELogType.Informtation,
                            DMELogPriority.High,
                            request.SplitTaskWk
                        );

                    // Get the page count of the input document...
                    int cFilePages = in_doc.GetPageCount();

                    if (requireNewFile)
                    {
                        merged_doc = new PDF.PDFDoc();
                        requireNewFile = false;
                    }

                    if (pageCount + cFilePages <= spoolSize)
                    {
                        // Merge the contents to new file...
                        merged_doc.InsertPages
                            (
                                // ...the page to insert after...
                                pageCount == 0 ? 0 : pageCount + 1,

                                // ...the input document...
                                in_doc,

                                // ...start page in input document...
                                startPage,

                                // ...number of pages in input document...
                                cFilePages,

                                // ...flags
                                PDF.PDFDoc.InsertFlag.e_none
                            );

                        //startPage = merged_doc.GetPageCount() + 1;
                        pageCount = merged_doc.GetPageCount();  //+= cFilePages;

                        // Close our input file...Get the next file...
                        in_doc.Close();
                        in_doc.Dispose();

                        // Save the file?
                        if (pageCount == spoolSize || fileCount == request.InputFiles.Length)
                        {
                            // Save our PDF...
                            SavePDF(merged_doc, inputFile, numberedOutput, request.SplitTaskWk);
                            merged_doc.Close();
                            merged_doc = null;

                            if (pageCount == spoolSize)
                                doc_num++;

                            startPage = 1;
                            pageCount = 0;

                            if (fileCount < request.InputFiles.Length)
                                requireNewFile = true;
                        }
                        else
                            startPage = pageCount + 1;
                    }
                    else
                    {
                        int loadPages = 0;
                        int maxLoadPages = (spoolSize - pageCount);
                        if (maxLoadPages > cFilePages)
                            loadPages = cFilePages;
                        else
                            loadPages = maxLoadPages;

                        // Merge the contents to new file...
                        merged_doc.InsertPages
                            (
                                // ...the page to insert after...
                                pageCount == 0 ? 0 : pageCount + 1,

                                // ...the input document...
                                in_doc,

                                // ...start page in input document...
                                startPage,

                                // ...number of pages in input document...
                                loadPages,  //((cFilePages + 1) - startPage) <= spoolSize ? (cFilePages - startPage) : spoolSize,

                                // ...flags
                                PDF.PDFDoc.InsertFlag.e_none
                            );


                        if (loadPages < cFilePages)
                        {
                            // Save our PDF...
                            SavePDF(merged_doc, inputFile, numberedOutput, request.SplitTaskWk);
                            merged_doc.Close();
                            merged_doc = null;

                            // Get New File...
                            doc_num++;
                            pageCount = 0;
                            startPage = 1;
                            int oldLoadPages = loadPages;
                            loadPages = cFilePages - oldLoadPages;

                            numberedOutput = string.Format("{0}_{1}.pdf", request.OutputPath, doc_num + 1);

                            merged_doc = new PDF.PDFDoc();
                            //merged_doc.InitSecurityHandler();


                            // Write the remaining pages to our new file
                            merged_doc.InsertPages
                                (
                                    // ...the page to insert after...
                                    pageCount == 0 ? 0 : pageCount + 1,

                                    // ...the input document...
                                    in_doc,

                                    // ...start page in input document...
                                    startPage,

                                    // ...number of pages in input document...
                                    loadPages,  //((cFilePages + 1) - startPage) <= spoolSize ? (cFilePages - startPage) : spoolSize,

                                    // ...flags
                                    PDF.PDFDoc.InsertFlag.e_none
                                );

                            // Get our page count...
                            pageCount = merged_doc.GetPageCount();

                        }
                        else if (fileCount == request.InputFiles.Length)
                        {
                            // Save our PDF...
                            SavePDF(merged_doc, inputFile, numberedOutput, request.SplitTaskWk);
                            merged_doc.Close();
                            merged_doc = null;
                        }
                        else
                            pageCount += cFilePages;
                    }
                    in_doc.Close();
                    in_doc.Dispose();
                }

                // Remove the original files?
                if (request.DeleteOriginals)
                    RemoveOriginalPDFFiles(request.InputFiles, request.SplitTaskWk, "Deleting original files: {0}");

                if (doc_num == 0)
                {
                    // Remove _1 from merged file
                    string savedFile = string.Format("{0}_{1}.pdf", request.OutputPath, doc_num + 1);
                    string newFile = string.Format("{0}.pdf", request.OutputPath);

                    // Delete destination file if it already exists...
                    if (System.IO.File.Exists(newFile))
                        File.Delete(newFile);

                    // Rename the file...
                    File.Move(savedFile, newFile);
                }

                retVal = true;
            }
            catch (Exception ex)
            {
                // Log failure
                DMELoggerExtension.LogException
                    (
                        ex,
                        DMELogType.Exception,
                        DMELogPriority.High,
                        request.SplitTaskWk
                    );

                // rethrow the exception...
                throw;
            }

            DMELoggerExtension.LogInformation
                (
                    // Log what we're doing...
                    string.Format("End MergePDFDocuments: {0}, {1}", retVal ? "Success" : "Failure", request.InputFiles.ToLogString()),
                        DMELogType.Informtation,
                        DMELogPriority.High,
                        request.SplitTaskWk
                );

            return retVal;
        }
            catch (Exception ex)
            {
                DMELoggerExtension.LogInformation(string.Format("Error MergePDFDocuments - {0}", ex.Message), DMELogType.Exception, DMELogPriority.High, request.TaskWk);
                throw;
            }
        }

        public int GetPDFFileCount(string inputFile)
        {
            try
            {
            string PDFTronKey = _config.PDFTronKey;
            PDFNet.Initialize(PDFTronKey);
            PDF.PDFDoc in_doc = null;
            in_doc = new PDF.PDFDoc(inputFile);

            int pageCount = in_doc.GetPageCount();

            using (var doc = new PDF.PDFDoc(inputFile))
            {
                var page = doc.GetPage(1);

                using (var txt = new PDF.TextExtractor())
                {
                    txt.Begin(page);
                    var data = txt.GetAsText();

                }
            }

            in_doc.Close();
            in_doc.Dispose();

            return pageCount;
        }
            catch (Exception ex)
            {
                DMELoggerExtension.LogInformation(string.Format("Error GetPDFFileCount - {0}", ex.Message), DMELogType.Exception, DMELogPriority.High);
                throw;
            }
        }

        public bool MergePDFDocumentsInMailstreamOrder(XmpieJobWorkItem workItem, string[] inputFiles, string outputFile, 
            bool deleteOriginals, bool sourceLocationOverride, string overrideLocation, int? mergeBatchsize, bool commingleOnly, 
            bool deleteInputFiles, string docId, string fileAppend, List<string> deleteFiles, int pagesPerContact, int taskTrackingRequestWk)
        {
            bool retVal = false;
            try
            {
                // Log what we're doing...
                DMELoggerExtension.LogInformation(string.Format("Begin MergePDFDocumentsInMailstreamOrder - WorkItem: {0}; pagesPerContact: {1}; inputFiles: {2}; outputFile: {3}", JsonConvert.SerializeObject(workItem), pagesPerContact, string.Join(",", inputFiles), outputFile), DMELogType.Informtation, DMELogPriority.High, workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null);

                _vdfProvider.UpdateTaskStatusTracking(taskTrackingRequestWk, "X", "MergePDFDocumentsInMailstreamOrder - Started");

                var cancellationToken = default(CancellationToken);

                // Log what we're doing...
                DMELoggerExtension.LogInformation
                    (
                        string.Format("Begin Creating Single PDF in Mailstream Order - Input: {0}, Output: {1}, DeleteOriginals: {2}, SplitTaskWk: {3}",
                        inputFiles.ToLogString(), outputFile, deleteOriginals, workItem.CifTaskWk),
                            DMELogType.Informtation,
                            DMELogPriority.High,
                            workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                    );

                var stitchConfig = _vdfProvider.GetOnDemandMailStreamStitchConfig(workItem.VdfJobWk, _config.OnDemandFile_OutPutType).Result;

                DMELoggerExtension.LogInformation
                    (
                        JsonConvert.SerializeObject(stitchConfig),
                            DMELogType.Informtation,
                            DMELogPriority.High,
                            workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                    );

                var totalMergeFiles = 0;
                var sortGroupDefnSpoolSize = stitchConfig.SpoolSize;
                var origOutput = outputFile;
                var PDFTronKey = _config.PDFTronKey;
                Stopwatch timePerFile;

                PDFNet.Initialize(PDFTronKey);

                var mailorder = _vdfProvider.GetMailstreamFilenames
                    (
                        workItem.VdfJobWk,
                        sortGroupDefnSpoolSize,
                        sourceLocationOverride ? overrideLocation : _config.OnDemandFileStore_PDFVT.Replace("@vdf_job_wk", workItem.VdfJobWk.ToString()) + "\\",
                        commingleOnly,
                        fileAppend
                    ).Result;

                DMELoggerExtension.LogInformation
                    (
                        string.Format("[Mailstream2Filename Records Loaded: {0}] [Pages Per Contact: {1}]", mailorder.Count, pagesPerContact),
                            DMELogType.Informtation,
                            DMELogPriority.High,
                            workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                    );

                // throw error is cancellation requested
                cancellationToken.ThrowIfCancellationRequested();

                //log and skip task if count == 0
                if (mailorder != null && mailorder.Count == 0)
                {
                    DMELoggerExtension.LogInformation
                    (
                          string.Format("[Mailstream2Filename No commingle files to merge. Skipping task] commingleOnly: {0}", commingleOnly),
                              DMELogType.Informtation,
                              DMELogPriority.High,
                              workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                    );
                    retVal = true;
                }
                else if (mailorder != null && mailorder.Count > 0)
                {
                    try
                    {
                        int spoolSize = sortGroupDefnSpoolSize * pagesPerContact;
                        int workLoad = mergeBatchsize ?? _config.MergeFileContacts;
                        int maxRetry = 5;

                        // chunk the mailorder list into workloads
                        var requestItems = new List<Mailstream2FilenameRequest> { };
                        var mailOrderChunks = mailorder.ChunkBy(workLoad).ToList();
                        totalMergeFiles = mailOrderChunks.Count;

                        DMELoggerExtension.LogInformation
                            (
                                    string.Format("Total chunks count: {0}", totalMergeFiles),
                                    DMELogType.Informtation,
                                    DMELogPriority.High,
                                    workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                            );

                        for (var k = 0; k < totalMergeFiles; k++)
                        {
                            var mailOrderChunk = mailOrderChunks[k];
                            requestItems.Add(new Mailstream2FilenameRequest
                            {
                                JobWk = workItem.VdfJobWk,
                                FileIndex = k + 1,
                                IsCommingle = commingleOnly,
                                MinPresortId = mailOrderChunk.Select(x => x.presortId).Min(),
                                MaxPresortId = mailOrderChunk.Select(x => x.presortId).Max()
                            });
                        }

                        // log request
                        DMELoggerExtension.LogInformation
                            (
                                    JsonConvert.SerializeObject(requestItems),
                                    DMELogType.Informtation,
                                    DMELogPriority.High,
                                    workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                            );

                        _vdfProvider.OnDemandMergeFileMailStreamAddRequests(workItem.VdfJobWk, workItem.CifTaskWk, commingleOnly, JsonConvert.SerializeObject(requestItems)).Wait();

                        var mailStreamRequests = _vdfProvider.GetOnDemandMergeFileMailStreamRequests(workItem.VdfJobWk, workItem.CifTaskWk, commingleOnly).Result;

                        var mailOrderChunkItemIndex = 0;

                        foreach (var mailOrderChunkItem in mailOrderChunks)
                        {
                            mailOrderChunkItemIndex++;
                            var mergeFileRetryCount = 0;
                            var mergeOutputFile = string.Format("{0}_{1}.pdf", origOutput, mailOrderChunkItemIndex);

                            DMELoggerExtension.LogInformation
                                (
                                    string.Format("Processing merge file '{0}'...", mailOrderChunkItemIndex),
                                    DMELogType.Informtation,
                                    DMELogPriority.High,
                                    workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                                );

                            // If request already processed, then no need to process again
                            if (mailStreamRequests.Any(x => x.FileIndex == mailOrderChunkItemIndex && x.StatusIndicator == "C"))
                            {
                                DMELoggerExtension.LogInformation
                                (
                                    string.Format("Merge file '{0}' was already processed.", mailOrderChunkItemIndex),
                                    DMELogType.Informtation,
                                    DMELogPriority.High,
                                    workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                                );
                                continue;
                            }
                            
                            for (var i = 0; i < maxRetry; i++)
                            {
                                try
                                {
                                    // Update status to procesing..
                                    _vdfProvider.OnDemandMergeFileMailStreamUpdateStatus(workItem.VdfJobWk, workItem.CifTaskWk, commingleOnly, mailOrderChunkItemIndex, "X", "Processing...", mergeFileRetryCount).Wait();

                                    DMELoggerExtension.LogInformation
                                    (
                                        string.Format("Updated status to processing: File Index : {0}. Max Retry Count: {1}.", mailOrderChunkItemIndex, mergeFileRetryCount),
                                        DMELogType.Informtation,
                                        DMELogPriority.High,
                                        workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                                    );

                                    using (var mergedDoc = new PDFDoc())
                                    {
                                        // Initializes document's SecurityHandler.
                                        mergedDoc.InitSecurityHandler();
                                        var hasFoundMergeRecords = true; ;
                                        foreach (var mergeItem in mailOrderChunkItem)
                                        {
                                            if (_config.VerboseLogging)
                                            {
                                                DMELoggerExtension.LogInformation
                                                (
                                                    string.Format("Processing... {0}", JsonConvert.SerializeObject(mergeItem)),
                                                    DMELogType.Informtation,
                                                    DMELogPriority.High,
                                                    workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                                                );
                                            }

                                            var recordFileLocationIndex = mergeItem.GetFileIndex(inputFiles);
                                            if (_config.VerboseLogging)
                                            {
                                                DMELoggerExtension.LogInformation
                                                (
                                                    string.Format("Got Record Index : {0}. Max Retry Count: {1}.", recordFileLocationIndex, mergeFileRetryCount),
                                                    DMELogType.Informtation,
                                                    DMELogPriority.High,
                                                    workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                                                );
                                            }
                                            if (recordFileLocationIndex == -1)
                                            {
                                                hasFoundMergeRecords = false;
                                                break;
                                            }
                                            else
                                            {
                                                var pageIndex = (mergeItem.rowNumber) * pagesPerContact;
                                                var startPage = mergeItem.rowNumber == 1 ? 1 : pageIndex - pagesPerContact + 1;

                                                if (_config.VerboseLogging)
                                                {
                                                    DMELoggerExtension.LogInformation
                                                    (
                                                        string.Format("Page Index : {0}. Start Page: {1}. File: {2}. Max Retry Count: {3}.", pageIndex, startPage, inputFiles[recordFileLocationIndex], mergeFileRetryCount),
                                                        DMELogType.Informtation,
                                                        DMELogPriority.High,
                                                        workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                                                    );
                                                }

                                                using (var inDoc = new PDFDoc(inputFiles[recordFileLocationIndex]))
                                                {
                                                    var copyPages = new ArrayList();
                                                    for (var k = 0; k < pagesPerContact; k++)
                                                    {
                                                        copyPages.Add(inDoc.GetPage(startPage + k));

                                                        if (_config.VerboseLogging)
                                                        {
                                                            DMELoggerExtension.LogInformation
                                                            (
                                                                string.Format("Got copy page. Page#: {0}. Max Retry Count: {1}.", k, mergeFileRetryCount),
                                                                DMELogType.Informtation,
                                                                DMELogPriority.High,
                                                                workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                                                            );
                                                        }
                                                    }

                                                    var importedPages = mergedDoc.ImportPages(copyPages);

                                                    if (_config.VerboseLogging)
                                                    {
                                                        DMELoggerExtension.LogInformation
                                                        (
                                                            string.Format("Imported Pages Count: {0}. Max Retry Count: {1}.", importedPages.Count, mergeFileRetryCount),
                                                            DMELogType.Informtation,
                                                            DMELogPriority.High,
                                                            workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                                                        );
                                                    }

                                                    for (var y = 0; y != importedPages.Count; y++)
                                                    {
                                                        mergedDoc.PagePushBack((PDF.Page)importedPages[y]);

                                                        if (_config.VerboseLogging)
                                                        {
                                                            DMELoggerExtension.LogInformation
                                                            (
                                                                string.Format("Merged Doc has imported pages. Merged Doc Count {0}. Import Page Index: {1} . Max Retry Count: {2}.", mergedDoc.GetPageCount(), y, mergeFileRetryCount),
                                                                DMELogType.Informtation,
                                                                DMELogPriority.High,
                                                                workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                                                            );
                                                        }
                                                    }
                                                        
                                                    copyPages = null;
                                                    importedPages = null;
                                                }
                                            }
                                        }

                                        // Get our page count...
                                        var mergedDocPageCount = mergedDoc.GetPageCount();

                                        DMELoggerExtension.LogInformation
                                        (
                                            string.Format("Merge File '{0}' Page Count {1} : Retries: {2}. Max Retry Count: {3}.", mailOrderChunkItemIndex, mergedDocPageCount, mergeFileRetryCount, maxRetry),
                                            DMELogType.Informtation,
                                            DMELogPriority.High,
                                            workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                                        );

                                        if (hasFoundMergeRecords)
                                        {
                                            // Save Merge File
                                            SavePDF(mergedDoc, null, mergeOutputFile, workItem.CifTaskWk, true, CancellationToken.None, false);

                                            // Update status to complete..
                                            _vdfProvider.OnDemandMergeFileMailStreamUpdateStatus(workItem.VdfJobWk, workItem.CifTaskWk, commingleOnly, mailOrderChunkItemIndex, "C", "Successfully processed", mergeFileRetryCount).Wait();
                                        }
                                        else
                                        {
                                            // Update status to Error..
                                            _vdfProvider.OnDemandMergeFileMailStreamUpdateStatus(workItem.VdfJobWk, workItem.CifTaskWk, commingleOnly, mailOrderChunkItemIndex, "E", "File not found", mergeFileRetryCount).Wait();
                                        }

                                        break;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    // Increment our retry counter...
                                    mergeFileRetryCount++;

                                    DMELoggerExtension.LogInformation
                                    (
                                        string.Format("Retrying. Merge File exception: Retries: {0}. Max Retry Count: {1}. Error: {2}.", mergeFileRetryCount, maxRetry, ex.Message),
                                        DMELogType.Exception,
                                        DMELogPriority.High,
                                        workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                                    );

                                    DMELoggerExtension.LogException
                                    (
                                        ex,
                                        DMELogType.Exception,
                                        DMELogPriority.High,
                                        workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                                    );

                                    if (mergeFileRetryCount < maxRetry)
                                    {
                                        // Update status to retry..
                                        _vdfProvider.OnDemandMergeFileMailStreamUpdateStatus(workItem.VdfJobWk, workItem.CifTaskWk, commingleOnly, mailOrderChunkItemIndex, "R", ex.Message, mergeFileRetryCount).Wait();

                                        // Sleep for 2 seconds...
                                        Thread.Sleep(2000);

                                    }
                                    else
                                    {
                                        // Update status to error..
                                        _vdfProvider.OnDemandMergeFileMailStreamUpdateStatus(workItem.VdfJobWk, workItem.CifTaskWk, commingleOnly, mailOrderChunkItemIndex, "E", ex.Message, mergeFileRetryCount).Wait();
                                        break;
                                    }
                                }
                            }
                        }

                        DMELoggerExtension.LogInformation(string.Format("Finished saving all merge files [Count: {0}]", mailOrderChunks.Count), DMELogType.Informtation, DMELogPriority.High, workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null);

                        DMELoggerExtension.LogInformation(string.Format("Sleeping for {0} second(s)...", 120), DMELogType.Debug, DMELogPriority.High, workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null);
                        Thread.Sleep(120 * 1000);

                    }
                    catch (Exception ex)
                    {
                        // Log failure
                        DMELoggerExtension.LogException
                            (
                                ex,
                                DMELogType.Exception,
                                DMELogPriority.High,
                                workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                            );

                        // throw back the exception
                        throw;
                    }

                    try
                    {
                        // Get the list of mergeset files...
                        var mailStreamFiles = new List<string>();
                        for (int x = 1; x <= totalMergeFiles; x++)
                        {
                            var outputMergeFilePath = GetMergeFileOutputPath(string.Format("{0}_{1}.pdf", origOutput, x), workItem.VdfJobWk);
                            mailStreamFiles.Add(outputMergeFilePath);
                        }

                        var outputFileOnDemandJobName = workItem.OnDemandJobName;
                        if (commingleOnly)
                        {
                            outputFileOnDemandJobName = workItem.OnDemandJobName + "_commingle";
                        }

                        // Merge our mergeset files together for batchsize...
                        if (mailStreamFiles.Count > 0)
                            MergePDFDocuments(new PDFMergeRequest
                            {
                                InputFiles = mailStreamFiles.ToArray(),
                                OutputPath = _config.OnDemandFileStore_PDFVT.Replace("@vdf_job_wk", workItem.VdfJobWk.ToString()) + "\\" + outputFileOnDemandJobName.Substring(outputFileOnDemandJobName.LastIndexOf("\\") + 1),
                                DeleteOriginals = false,
                                SplitTaskWk = workItem.CifTaskWk,
                                SpooSize = sortGroupDefnSpoolSize,
                                PerContactPages = pagesPerContact
                            });

                        // Delete mergeset files too!
                        if (deleteOriginals)
                        {
                            DMELoggerExtension.LogInformation
                                (
                                    "Start deleting merge files",
                                    DMELogType.Informtation,
                                    DMELogPriority.High,
                                    workItem.CifTaskWk > 0 ? (int?)workItem.CifTaskWk : null
                                );
                            RemoveOriginalPDFFiles(mailStreamFiles.ToArray(), workItem.CifTaskWk,
                                "Deleting merge files: {0}");
                        }
                        retVal = true;
                    }
                    catch (Exception ex)
                    {
                        // Log failure
                        DMELoggerExtension.LogException
                            (
                                ex,
                                DMELogType.Exception,
                                DMELogPriority.High,
                                workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                            );

                        // rethrow the exception...
                        throw;
                    }
                }
                else
                {
                    DMELoggerExtension.LogInformation
                        (
                            // Log what we're doing...
                            string.Format("Failure: 0 Mailstream2Filename records loaded."),
                                DMELogType.Informtation,
                                DMELogPriority.High,
                                workItem.CifTaskWk > 0 ? (int?)workItem.CifTaskWk : null
                        );

                    retVal = false;
                }

                // Delete originals (if required)
                if (deleteInputFiles)
                {
                    DMELoggerExtension.LogInformation
                          (
                              "Start deleting original files",
                              DMELogType.Informtation,
                              DMELogPriority.High,
                               workItem.CifTaskWk > 0 ? (int?)workItem.CifTaskWk : null
                          );
                    // try multiple times to delete the file if it fails
                    bool deleteSuccess = false;
                    int deleteCounter = 0;


                    while (!deleteSuccess)
                    {
                        deleteCounter++;
                        DMELoggerExtension.LogInformation
                        (
                            "Start deleting original files: Retry: " + deleteCounter,
                            DMELogType.Informtation,
                            DMELogPriority.High,
                            workItem.CifTaskWk > 0 ? (int?)workItem.CifTaskWk : null
                        );

                        deleteSuccess = DeleteOriginalPDFFiles(workItem.VdfJobWk, deleteFiles.ToArray(), workItem.CifTaskWk, "Deleting original files: {0}");

                        if (deleteCounter >= 10) //try 10 times
                        {
                            deleteSuccess = true;
                        }
                        if (!deleteSuccess)
                        {
                            Thread.Sleep(60000); // sleep for 60 secs
                        }
                    }


                    DMELoggerExtension.LogInformation
                          (
                              "completed deleting original files",
                              DMELogType.Informtation,
                              DMELogPriority.High,
                              workItem.CifTaskWk > 0 ? (int?)workItem.CifTaskWk : null
                          );
                    // Move files to the root folder
                    var copyFromfolder = _config.OnDemandFileStore_Local.Replace("@vdf_job_wk", workItem.VdfJobWk.ToString()) + "\\generated";
                    var copyTofolder = _config.OnDemandFileStore_PDFVT.Replace("@vdf_job_wk", workItem.VdfJobWk.ToString());

                    DirectoryInfo dFrom = new DirectoryInfo(copyFromfolder);


                    DMELoggerExtension.LogInformation
                        (
                               string.Format("Start copying stitch files: copy From: {0}, copyTo: {1} ", copyFromfolder, copyTofolder),
                                  DMELogType.Informtation,
                                  DMELogPriority.High,
                                  workItem.CifTaskWk > 0 ? (int?)workItem.CifTaskWk : null
                              );

                    foreach (FileInfo fi in dFrom.GetFiles())
                    {
                        var fullPath = copyTofolder + "\\" + fi.Name;
                        if (File.Exists(fullPath))
                        {
                            File.Delete(fullPath);
                        }
                        DMELoggerExtension.LogInformation(String.Format("Copying file from {0} to {1}. {2} bytes.", fi.FullName, fullPath, fi.Length), DMELogType.Informtation, DMELogPriority.High, workItem.CifTaskWk > 0 ? (int?)workItem.CifTaskWk : null);
                        timePerFile = Stopwatch.StartNew();
                        fi.MoveTo(fullPath);

                        timePerFile.Stop();
                        DMELoggerExtension.LogInformation(String.Format("Copied file from {0} to {1}. Took {2} seconds", fi.FullName, fullPath, timePerFile.Elapsed.TotalSeconds), DMELogType.Informtation, DMELogPriority.High, workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null);

                    }

                    DMELoggerExtension.LogInformation
                        (
                         string.Format("completed copying stitch files: copy From: {0}, copyTo: {1} ", copyFromfolder, copyTofolder),
                            DMELogType.Informtation,
                            DMELogPriority.High,
                            workItem.CifTaskWk > 0 ? (int?)workItem.CifTaskWk : null
                        );

                    DMELoggerExtension.LogInformation
                        (
                            "Start Delete of from folder",
                            DMELogType.Informtation,
                            DMELogPriority.High,
                            workItem.CifTaskWk > 0 ? (int?)workItem.CifTaskWk : null
                        );

                    dFrom.Delete();


                    DMELoggerExtension.LogInformation
                        (
                            "Completed delete of from folder",
                            DMELogType.Informtation,
                            DMELogPriority.High,
                            workItem.CifTaskWk > 0 ? (int?)workItem.CifTaskWk : null
                        );

                }

                DMELoggerExtension.LogInformation
                    (
                        // Log what we're doing...
                        string.Format("End Create Single PDF in Mailstream Order: {0}, {1}", retVal ? "Success" : "Failure", inputFiles.ToLogString()),
                            DMELogType.Informtation,
                            DMELogPriority.High,
                            workItem.CifTaskWk > 0 ? (int?)workItem.CifTaskWk : null
                    );

            }
            catch (Exception ex)
            {
                retVal = false;
                // Log failure
                DMELoggerExtension.LogException
                    (
                        ex,
                        DMELogType.Exception,
                        DMELogPriority.High,
                        workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                    );
            }

            if (retVal)
            {
                _vdfProvider.UpdateTaskStatusTracking(taskTrackingRequestWk, "C", "Processed request successfully.");
                _vdfProvider.OnDemandMergeFileMailStreamDeleteRequests(workItem.VdfJobWk, workItem.CifTaskWk, commingleOnly).Wait();
            }
            else
            {
                _vdfProvider.UpdateTaskStatusTracking(taskTrackingRequestWk, "E", "Failed to process request.");
            }
            
            return retVal;
        }

        private string GetMergeFileOutputPath(string outputFile, int vdfJobWk)
        {
            var returnValue = outputFile;

            if (File.Exists(outputFile))
                return returnValue;

            var destinationServers = _config.OnDemandFileStore_UNC.Replace("@vdf_job_wk", vdfJobWk.ToString());
            var fileName = Path.GetFileName(outputFile);
            foreach (var destinationPath in destinationServers.Split(';'))
            {
                var destinationPathGenerated = destinationPath + "\\generated\\" + fileName;
                if (File.Exists(destinationPathGenerated))
                {
                    returnValue = destinationPathGenerated;
                    break;
                }
            }

            return returnValue;
        }

        private void DeleteMergeFiles(int vdfJobWk)
        {
            var destinationServers = _config.OnDemandFileStore_UNC.Replace("@vdf_job_wk", vdfJobWk.ToString());
            foreach (var destinationPath in destinationServers.Split(';'))
            {
                var destinationPathGenerated = destinationPath + "\\generated";
                if (Directory.Exists(destinationPathGenerated))
                {
                    var di = new DirectoryInfo(destinationPathGenerated);

                    foreach (var file in di.GetFiles().Where(x => x.Name.ToLower().Contains("_merge")))
                    {
                        file.Delete();
                    }
                }
            }
        }

        public bool MergePDFDocumentsForODP(PDFMergeODPRequest request)
        {
            try
            {
            // Log what we're doing...
            DMELoggerExtension.LogInformation(string.Format("Begin MergePDFDocumentsForODP - {0}", JsonConvert.SerializeObject(request)), DMELogType.Informtation, DMELogPriority.High, request.TaskWk);

            int jobWk = request.JobWk;
            string fLocation = request.FLocation;
            string[] inputFiles = request.InputFiles;
            string outputFile = request.OutputFile;
            string msn = request.MSN;
            bool deleteOriginals = request.DeleteOriginals;
            bool sourceLocationOverride = request.SourceLocationOverride;
            string overrideLocation = request.OverrideLocation;
            int? mergeBatchsize = request.MergeBatchsize;
            int? taskWk = request.TaskWk;
            int batchSize = request.SpooSize;
            int pagesPerContact = request.PerContactPages;

            var parameters = $"jobwk {jobWk}, flocation: {fLocation}, msn: {msn}, deleteOriginals: {deleteOriginals}, sourceLocationOverride: {sourceLocationOverride}, overrideLocation: {overrideLocation}, mergeBatchsize: {mergeBatchsize}, taskWk: {taskWk}, inputFiles: {(inputFiles?.Length > 0 ? string.Join(",", inputFiles) : "null")}";

            // Log what we're doing...
            DMELoggerExtension.LogInformation
                (
                    string.Format("Begin Creating Single PDF For ODP - Input: {0}, Output: {1}, DeleteOriginals: {2} " + parameters, inputFiles.ToLogString(), outputFile, deleteOriginals),
                        DMELogType.Informtation,
                        DMELogPriority.High,
                        taskWk
                );

            

            int fileIndex = 0;
            bool retVal = false;

            string origOutput = outputFile;
            
            PDFNet.Initialize(_config.PDFTronKey);

            var commOrder = _vdfProvider.GetODPFilesInCommOrder(jobWk, msn, pagesPerContact).Result;

            if (commOrder != null)
            {
                DMELoggerExtension.LogInformation
                (
                    $"ODPCommunicationOrder Records Loaded: {parameters}  {commOrder.Count}",
                        DMELogType.Informtation,
                        DMELogPriority.High,
                        taskWk
                );
            }
            else
            {
                DMELoggerExtension.LogInformation
                (
                    "variable commOrder on JobControllerProvicer.cs, MergePDFDocumentsForODP() is null " + parameters,
                        DMELogType.Informtation,
                        DMELogPriority.High,
                        taskWk
                );
            }


            if (commOrder != null && commOrder.Count > 0)
            {
                PDF.PDFDoc in_doc = null;
                PDF.PDFDoc merged_doc = null;

                try
                {
                    int spoolSize = (batchSize * pagesPerContact);
                    int pageCount = 0;
                    int oldFL = -2;
                    int workLoad = mergeBatchsize ?? _config.MergeFileContacts;

                    int startCount = 0;
                    int maxCounter = 0;
                    int maxRetry = 5;

                    DMELoggerExtension.LogInformation
                    (
                        string.Format("spoolSize: {0}, workLoad: {1} JobControllerProvicer.cs, MergePDFDocumentsForODP() is null " + parameters, spoolSize, workLoad),
                            DMELogType.Informtation,
                            DMELogPriority.High,
                            taskWk
                    );

                    for (; ; )
                    {
                        int retryCount = 0;

                        maxCounter = commOrder.Count > workLoad ? workLoad : commOrder.Count;
                        if ((commOrder.Count - startCount) < workLoad)
                            maxCounter = (commOrder.Count - startCount);

                        fileIndex++;
                        outputFile = string.Format("{0}_{1}.pdf", origOutput, fileIndex);

                        merged_doc = new PDF.PDFDoc();
                        merged_doc.InitSecurityHandler();

                        // Work through the list of mailstream files to create our complete output file
                        for (int z = 0; z != maxCounter; z++)
                        {
                            ODPCommunicationOrder mfile = commOrder[startCount + z];

                            // Locate the required file
                            int fileLocation = -1;
                            fileLocation = Array.FindIndex(inputFiles, x => x.ToLower().Equals(mfile.Filename.ToLower()));

                            int pageIndex = mfile.startPage * pagesPerContact;
                            int startPage = mfile.startPage == 1 ? 1 : pageIndex - 1;

                            if (fileLocation == -1)
                            {
                                // Something went wrong...Log what we're doing...
                                DMELoggerExtension.LogInformation
                                    (
                                        string.Format("File {0} was not found within the input file array. " + parameters, mfile.Filename),
                                            DMELogType.Informtation,
                                            DMELogPriority.High,
                                            taskWk
                                    );

                                // Throw file not found exception
                                throw new Exception(string.Format("File {0} not found.  Merge aborted. " + parameters, mfile.Filename));
                            }
                            else if (fileLocation != oldFL)
                            {
                                    /*
                                    DMELoggerExtension.LogInformation
                                        (
                                            string.Format("Getting pages from file: {0} " + parameters, inputFiles[fileLocation]),
                                            DMELogType.Informtation,
                                            DMELogPriority.High,
                                            taskWk
                                        );*/

                                if (oldFL != -2)
                                {
                                    in_doc.Close();
                                    in_doc.Dispose();
                                    in_doc = null;
                                }

                                try
                                {
                                    oldFL = fileLocation;
                                    in_doc = new PDF.PDFDoc(string.Format(@"{0}\{1}", fLocation, inputFiles[fileLocation]));
                                }
                                catch (Exception ex)
                                {
                                    DMELoggerExtension.LogException
                                    (
                                        ex,
                                        DMELogType.Exception,
                                        DMELogPriority.High,
                                        taskWk
                                    );

                                    DMELoggerExtension.LogInformation
                                    (
                                        string.Format("PDF Location threw an exception when attempting to reference: '{0}' " + parameters, string.Format(@"{0}\{1}", fLocation, inputFiles[fileLocation])),
                                        DMELogType.Exception,
                                        DMELogPriority.High,
                                        taskWk
                                    );
                                }

                                int docpages = in_doc.GetPageCount();
                            }

                            for (; ; )
                            {
                                try
                                {
                                    ArrayList copyPages = new ArrayList();
                                    copyPages.Add(in_doc.GetPage(startPage));
                                    copyPages.Add(in_doc.GetPage(startPage + (pagesPerContact - 1)));

                                    ArrayList importedPages = merged_doc.ImportPages(copyPages);
                                    for (int y = 0; y != importedPages.Count; y++)
                                        merged_doc.PagePushBack((PDF.Page)importedPages[y]);

                                    copyPages = null;
                                    importedPages = null;

                                    break;
                                }
                                catch (Exception ex)
                                {
                                    // Log what we're doing...
                                    retryCount++;
                                    string msg = string.Empty;

                                    if (retryCount < maxRetry)
                                        msg = string.Format("Error encountered while creating merge file. Exception: {0};\nCurrent Page Count: {1};\nRetry count: {2} {3}", ex.Message, pageCount, retryCount, parameters);
                                    else
                                        throw new Exception(string.Format("Error encountered while creating merge file.\nException: {0};\nCurrent Page Count: {1};\nMaximum {2} Retries reached. Aborting. {4}", ex.Message, pageCount, maxRetry, parameters));

                                    DMELoggerExtension.LogInformation
                                        (
                                            msg,
                                            DMELogType.Exception,
                                            DMELogPriority.High,
                                            taskWk
                                        );
                                }
                            }

                            // Get our page count...
                            pageCount = merged_doc.GetPageCount();
                        }

                        try
                        {
                            SavePDF(merged_doc, null, outputFile, taskWk, true);
                        }
                        catch (Exception ex)
                        {
                            //log error
                            DMELoggerExtension.LogInformation
                             (
                               string.Format("Save PDF on  MergePDFDocumentsForODP() is NULL {0}. {1}", parameters, ex.Message),
                                DMELogType.Informtation,
                                DMELogPriority.High,
                                taskWk
                              );
                            throw;
                        }

                        merged_doc = null;

                        if ((workLoad * fileIndex) == commOrder.Count)
                            break;

                        if (pageCount == (commOrder.Count * pagesPerContact))
                            break;

                        if ((startCount + maxCounter) == commOrder.Count)
                            break;

                        if (maxCounter < workLoad || maxCounter == commOrder.Count)
                            break;

                        startCount += workLoad;
                    }
                }
                catch (Exception ex)
                {
                    // Log failure
                    DMELoggerExtension.LogException
                        (
                            ex,
                            DMELogType.Exception,
                            DMELogPriority.High,
                            taskWk
                        );

                    throw;
                }

                try
                {
                    // Close the last file...else we can't delete it!
                    in_doc.Close();
                    in_doc.Dispose();
                    in_doc = null;

                    // Delete originals (if required)
                    if (deleteOriginals)
                    {
                        // Add the fileLocation to the filename...
                        for (int x = 0; x != inputFiles.Length; x++)
                            inputFiles[x] = string.Format(@"{0}\{1}", fLocation, inputFiles[x]);

                        // Now remove them!
                        RemoveOriginalPDFFiles(inputFiles, taskWk, "Deleting original files: {0}");
                    }

                    // Get the list of mergeset files...
                    List<string> mailStreamFiles = new List<string>();
                    for (int x = 1; x != fileIndex + 1; x++)
                        mailStreamFiles.Add(string.Format("{0}_{1}.pdf", origOutput, x));

                    DMELoggerExtension.LogInformation(
                        $"Got {mailStreamFiles.Count} files to merge: [{mailStreamFiles.ToArray().ToLogString()}], origOutput: {origOutput}, fileIndex: {fileIndex}",
                        DMELogType.Informtation, DMELogPriority.High, taskWk);

                    // Merge our mergeset files together for batchsize...
                    if (mailStreamFiles.Count > 0)
                        MergePDFDocuments(new PDFMergeRequest { InputFiles = mailStreamFiles.ToArray(), OutputPath = origOutput, DeleteOriginals = false });

                    // Delete mergeset files too!
                    if (deleteOriginals)
                        RemoveOriginalPDFFiles(mailStreamFiles.ToArray(), taskWk, "Deleting merge files: {0}");

                    retVal = true;
                }
                catch (Exception ex)
                {
                    // Log failure
                    DMELoggerExtension.LogException
                        (
                            ex,
                            DMELogType.Exception,
                            DMELogPriority.High,
                            taskWk
                        );

                    // rethrow the exception...
                    throw;
                }
            }
            else
            {
                DMELoggerExtension.LogInformation
                    (
                        // Log what we're doing...
                        string.Format("Failure: 0 Mailstream2Filename records loaded. " + parameters),
                            DMELogType.Informtation,
                            DMELogPriority.High,
                            taskWk
                    );

                retVal = false;
            }

            string validationResult = ValidateFile(origOutput) ? "does exist" : "does not exist";
            DMELoggerExtension.LogInformation($"File {origOutput} Validation: The PDF file {validationResult}.", DMELogType.Informtation, DMELogPriority.High, taskWk);

            DMELoggerExtension.LogInformation
                (
                    // Log what we're doing...
                    string.Format("End Create Single PDF For ODP: {0}, {1} {2}", retVal ? "Success" : "Failure", inputFiles.ToLogString(), parameters),
                        DMELogType.Informtation,
                        DMELogPriority.High,
                        taskWk
                );

            return retVal;
        }
            catch (Exception ex)
            {
                DMELoggerExtension.LogInformation(string.Format("Error MergePDFDocumentsForODP - {0}", ex.Message), DMELogType.Exception, DMELogPriority.High, request.TaskWk);
                throw;
            }
        }

        public bool ValidateFile(string fileName, string extension = ".pdf")
        {
            if(Path.GetExtension(fileName) != extension)
            {
                fileName = $"{fileName}{extension}";
            }
            return File.Exists(fileName);
        }


        public bool MergeMergeFiles(MergeMergeFilesRequest request)
        {
            int? jobTaskWk = Int32.Parse(request.TaskWk) > -1 ? (int?)Int32.Parse(request.TaskWk) : null;
            try
            {
            // Log what we're doing...
            DMELoggerExtension.LogInformation(string.Format("Begin MergeMergeFiles - {0}", JsonConvert.SerializeObject(request)), DMELogType.Informtation, DMELogPriority.High, jobTaskWk);

            string jobWk = request.JobWk;
            string taskWk = request.TaskWk;
            string inputPath = request.InputPath;

            XmpieJobWorkItem workItem = null;
            bool retVal = false;

            try
            {
                DMELoggerExtension.LogInformation
                (
                    string.Format("Begin MergeMergeFiles: JobWk: {0}; TaskWk: {1}; Path: {2}", Int32.Parse(jobWk), Int32.Parse(taskWk), inputPath),
                        DMELogType.Informtation,
                        DMELogPriority.High,
                        Int32.Parse(taskWk) > -1 ? (int?)Int32.Parse(taskWk) : null
                );

                // Get out sort group information...
                var split = _vdfProvider.GetSortGroup(Int32.Parse(jobWk)).Result;

                // Create our workItem...
                workItem = new XmpieJobWorkItem
                {
                    CifTaskWk = Int32.Parse(taskWk),
                    VdfJobWk = Int32.Parse(jobWk)
                };


                // Get files...
                List<string> inputFiles = new List<string>();
                string fileTemplate = string.Format("{0}*_merge*.pdf", jobWk);
                string filePath = inputPath;
                DirectoryInfo di = new DirectoryInfo(filePath);

                for (int x = 1; x != di.GetFiles(fileTemplate).Length + 1; x++)
                    inputFiles.Add(string.Format("{0}\\{1}_{2}_merge_{3}.pdf", inputPath, jobWk, split.SortGroupName, x));

                // Define our output file...
                workItem.OnDemandJobName = string.Format("{0}\\{1}_{2}", inputPath, workItem.VdfJobWk, split.SortGroupName);
                string newOutput = workItem.OnDemandJobName;

                // get stitching configuration
                var stitchConfig = _vdfProvider.GetOnDemandMailStreamStitchConfig(workItem.VdfJobWk, _config.OnDemandFile_OutPutType).Result;
                
                PDFNet.Initialize(_config.PDFTronKey);

                int batchSize = stitchConfig.SpoolSize;

                // get pages per contact dynamically
                PDF.PDFDoc doc_pagesPerContact = new PDF.PDFDoc();
                doc_pagesPerContact.InitSecurityHandler();
                int doc_OrderQuantity = 0;
                foreach (var file in inputFiles)
                {
                    if (file.ToLower().Contains(string.Format("{0}_{1}", workItem.OnDemandJobName.ToLower(), stitchConfig.CatalogOrderWk)))
                    {
                        doc_pagesPerContact = new PDF.PDFDoc(file);
                        doc_OrderQuantity += doc_pagesPerContact.GetPageCount();
                    }
                }

                doc_pagesPerContact.Dispose();
                doc_pagesPerContact = null;

                // set pages per contact
                int pagesPerContact = doc_OrderQuantity / stitchConfig.OrderQuantity;


                DMELoggerExtension.LogInformation
                    (
                        string.Format("[Pages Per Contact: {0}][Output file(s): {1}_x.pdf]", pagesPerContact, workItem.OnDemandJobName),
                            DMELogType.Informtation,
                            DMELogPriority.High,
                            workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                    );

                // Merge files...
                    MergePDFDocuments(new PDFMergeRequest
                    {
                    InputFiles = inputFiles.ToArray(),
                    OutputPath = newOutput,
                    DeleteOriginals = _config.OnDemandStitchDeleteOriginals,
                    TaskWk = workItem.CifTaskWk,
                    SpooSize = batchSize,
                    PerContactPages = pagesPerContact
                });

                retVal = true;

                DMELoggerExtension.LogInformation
                    (
                        string.Format("End MergeMergeFiles"),
                            DMELogType.Informtation,
                            DMELogPriority.High,
                            workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                    );
            }
            catch (Exception ex)
            {
                // Log failure
                DMELoggerExtension.LogException
                    (
                        ex,
                        DMELogType.Exception,
                        DMELogPriority.High,
                        workItem.CifTaskWk > -1 ? (int?)workItem.CifTaskWk : null
                    );

                // rethrow the exception...
                throw;
            }

            return retVal;
        }
            catch (Exception ex)
            {
                DMELoggerExtension.LogInformation(string.Format("Error MergeMergeFiles - {0}", ex.Message), DMELogType.Exception, DMELogPriority.High, jobTaskWk);
                throw;
            }
        }

        public bool MarketNowProofPDFSplit(MarketNowProofPDFSplitRequest request)
        {
            try
            {
            DMELoggerExtension.LogInformation(string.Format("Entered MarketNowProofPDFSplit: {0}", JsonConvert.SerializeObject(request)), DMELogType.Informtation, DMELogPriority.High, request?.Request.SplitTaskWk);

            /* get proof file */
            var inputFiles = new List<string>();
            DirectoryInfo di = new DirectoryInfo(string.Format(@"{0}\", _config.OnDemandFileStore_PDFO));
            string fileTemplate = request.Request.SplitJobWk.ToString() + "*.pdf";
            foreach (FileInfo fi in di.GetFiles(fileTemplate).OrderBy(x => x.Name).ToList())
            {
                /* exclude sample file if any */
                if (!(fi.Name.ToLower().Contains("_sam")))
                {
                    if (!inputFiles.Any(x => x == fi.FullName))
                        inputFiles.Add(fi.FullName);
                }
            }

            DMELoggerExtension.LogInformation(string.Format("Pages per Contact {0}; Total Order Orgs {1}; Proof File Count {2}", request.PagePerContact, request.MarketNowDifProofJobs.Count, inputFiles.Count), DMELogType.Informtation, DMELogPriority.High, request.Request.SplitTaskWk);


            /* Validation */
            if (inputFiles.Count == 0)
            {
                // throw exception
                throw new Exception("NO PROOF  FILES FOUND TO PROCESS");
            }

            int pageCount = 0;
            int startPage = 1;

            /* Initialize PDFTron */
            PDFNet.Initialize(_config.PDFTronKey);

            PDF.PDFDoc merged_doc = new PDF.PDFDoc();
            PDF.PDFDoc in_doc = null;

            foreach (string inputFile in inputFiles)
            {
                // Open the file...
                in_doc = new PDF.PDFDoc(inputFile);


                // Get the page count of the input document...
                int cFilePages = in_doc.GetPageCount();


                merged_doc.InsertPages
                        (
                            // ...the page to insert after...
                            pageCount,

                            // ...the input document...
                            in_doc,

                            // ...start page in input document...
                            startPage,

                            // ...number of pages in input document...
                            cFilePages,

                            // ...flags
                            PDF.PDFDoc.InsertFlag.e_none
                        );


                pageCount = merged_doc.GetPageCount();

                // Close our input file...Get the next file...
                in_doc.Close();
                in_doc.Dispose();

            }

            // set page start
            startPage = 1;

            foreach (var difJob in request.MarketNowDifProofJobs)
            {
                ArrayList copyPages = new ArrayList();
                for (int k = 0; k < request.PagePerContact; k++)
                {
                    copyPages.Add(merged_doc.GetPage(startPage + k));
                }

                PDF.PDFDoc new_doc = new PDF.PDFDoc();

                for (int y = 0; y != copyPages.Count; y++)
                    new_doc.PagePushBack((PDF.Page)copyPages[y]);

                // get copy path
                string copypath = _config.OnDemandFileStore_CampaignProofs.Replace("@catalog_order_wk", difJob.CatalogOrderWk.ToString());
                string fullpath = string.Format(@"{0}\OnDemand_{1}_{2}.pdf", copypath, difJob.CatalogOrderWk, difJob.OrgWk);

                // check if folder exists
                bool folderExists = Directory.Exists(copypath);

                if (!folderExists)
                {
                    DMELoggerExtension.LogInformation(string.Format("BEGIN: Creating Folder: {0}", copypath), DMELogType.Informtation, DMELogPriority.High, request.Request.SplitTaskWk);
                    Directory.CreateDirectory(copypath);
                    DMELoggerExtension.LogInformation(string.Format("END: Created Folder: {0}", copypath), DMELogType.Informtation, DMELogPriority.High, request.Request.SplitTaskWk);
                }

                DMELoggerExtension.LogInformation(string.Format("BEGIN: Writing file to destination: {0}", fullpath), DMELogType.Informtation, DMELogPriority.High, request.Request.SplitTaskWk);

                if (File.Exists(fullpath))
                {
                    File.Delete(fullpath);
                    DMELoggerExtension.LogInformation(string.Format("Deleted existing {0} file", fullpath), DMELogType.Informtation, DMELogPriority.High, request.Request.SplitTaskWk);
                }

                // Optimize file...
                PDF.Optimizer.Optimize(new_doc);

                // save to disk
                new_doc.Save(fullpath, pdftron.SDF.SDFDoc.SaveOptions.e_compatibility);

                // close file and dispose
                new_doc.Close();
                new_doc.Dispose();

                DMELoggerExtension.LogInformation(string.Format("END: Writing file to destination: {0}", fullpath), DMELogType.Informtation, DMELogPriority.High, request.Request.SplitTaskWk);

                // Next set of pages
                startPage += request.PagePerContact;
            }


            // Close our merged file
            merged_doc.Close();
            merged_doc.Dispose();

            return true;
        }
            catch (Exception ex)
            {
                DMELoggerExtension.LogInformation(string.Format("Error MarketNowProofPDFSplit - {0}", ex.Message), DMELogType.Exception, DMELogPriority.High, request?.Request.SplitTaskWk);
                throw;
            }
        }

        public bool GenerateImageContactSheetProof(GenerateImageContactSheetProofRequest request)
        {
            try
            {
                DMELoggerExtension.LogInformation(string.Format("Entered GenerateImageContactSheetProof- {0}", JsonConvert.SerializeObject(request)), DMELogType.Informtation, DMELogPriority.High, request?.TaskWk);
            PDFNet.Initialize(_config.PDFTronKey);
            using (PDFDoc doc = new PDFDoc())
            using (ElementBuilder bld = new ElementBuilder())
            using (ElementWriter writer = new ElementWriter())
            {

                PDF.Page page = doc.PageCreate();
                writer.Begin(page);
                PDF.Element element;
                int x = 15;
                int y = 620;
                int i = 1;
                int row = 1;
                var prevCategoryWk = -2;
                var isFirstRow = true;
                var isCategoryPageBrk = false;
                foreach (var image in request.Images.OrderBy(r => r.CategoryWk).ToList())
                {
                    var categoryName = String.IsNullOrEmpty(image.CategoryName) ? "Default" : image.CategoryName;
                    var categoryWk = image.CategoryWk;

                    var pageHeader = string.Format("{0} - {1}", request.CampaignName, categoryName);

                    if (categoryWk != prevCategoryWk)
                    {
                        prevCategoryWk = categoryWk;
                        if (!isFirstRow)
                        {
                            x = 15;
                            y = 620;
                            i = 1;
                            row = 0;
                            writer.End();
                            doc.PagePushBack(page);
                            page = doc.PageCreate();
                            writer.Begin(page);
                            isCategoryPageBrk = true;
                        }
                        else
                        {
                            isFirstRow = false;
                        }

                        writer.WriteElement(bld.CreateTextBegin(PDF.Font.Create(doc, PDF.Font.StandardType1Font.e_times_roman), 14));
                        element = bld.CreateTextRun(pageHeader);
                        element.SetTextMatrix(1, 0, 0, 1, x + 10, y + 150);
                        writer.WriteElement(element);
                        writer.WriteElement(bld.CreateTextEnd());
                    }

                    Image img;
                    try
                    {
                        if (File.Exists(image.ImageUrl))
                        {
                            img = Image.Create(doc, image.ImageUrl);
                        }
                        else
                        {
                            img = Image.Create(doc, request.MissingImageUrl);
                        }

                    }
                    catch (Exception ex)
                    {
                        DMELoggerExtension.LogInformation(string.Format("Error: {0}", ex.Message), DMELogType.Exception, DMELogPriority.High, request?.TaskWk);
                        continue;
                    }
                    var imagewidth = img.GetImageWidth() / 2 > 185 ? 185 : img.GetImageWidth() / 2;
                    var imageheight = img.GetImageHeight() / 2 > 120 ? 120 : img.GetImageHeight() / 2;
                    element = bld.CreateImage(img, new Matrix2D(imagewidth, 0, 0, imageheight, x, y));
                    writer.WritePlacedElement(element);
                    var imagecode = image.ImageCode;

                    if (imagecode.Length > 40)
                    {
                        var displaytxt = imagecode.Substring(0, 40) + "-";
                        var len = imagecode.Length;
                        writer.WriteElement(bld.CreateTextBegin(PDF.Font.Create(doc, PDF.Font.StandardType1Font.e_times_roman), 7));
                        element = bld.CreateTextRun(displaytxt);
                        element.SetTextMatrix(1, 0, 0, 1, x, y + imageheight + 12);
                        writer.WriteElement(element);
                        writer.WriteElement(bld.CreateTextEnd());

                        displaytxt = "-" + imagecode.Substring(40, len - 40);
                        writer.WriteElement(bld.CreateTextBegin(PDF.Font.Create(doc, PDF.Font.StandardType1Font.e_times_roman), 7));
                        element = bld.CreateTextRun(displaytxt);
                        element.SetTextMatrix(1, 0, 0, 1, x, y + imageheight + 3);
                        writer.WriteElement(element);
                        writer.WriteElement(bld.CreateTextEnd());
                    }
                    else
                    {
                        writer.WriteElement(bld.CreateTextBegin(PDF.Font.Create(doc, PDF.Font.StandardType1Font.e_times_roman), 7));
                        element = bld.CreateTextRun(imagecode);
                        element.SetTextMatrix(1, 0, 0, 1, x + 3, y + imageheight + 5);
                        writer.WriteElement(element);
                        writer.WriteElement(bld.CreateTextEnd());

                    }

                    writer.WriteElement(element);
                    writer.WriteElement(bld.CreateTextEnd());

                    imagewidth = imagewidth < 170 ? 170 : imagewidth;
                    imageheight = imageheight < 115 ? 115 : imageheight;
                    x = x + imagewidth + 15;

                    if (i % 3 == 0)
                    {
                        y = y - (imageheight + 27);
                        x = 15;
                    }
                    if (row % 5 == 0 && i % 3 == 0 && !isCategoryPageBrk)
                    {
                        x = 15;
                        y = 620;
                        i = 1;
                        row = 0;
                        writer.End();
                        doc.PagePushBack(page);
                        page = doc.PageCreate();
                        writer.Begin(page);
                        isCategoryPageBrk = false;
                    }
                    i++;
                    if (x == 15)
                    {
                        row++;
                        i = 1;
                    }
                }
                writer.End();
                doc.PagePushBack(page);
                if (File.Exists(request.GeneratedPDFPath))
                {
                    File.Delete(request.GeneratedPDFPath);
                }

                doc.Save(request.GeneratedPDFPath, pdftron.SDF.SDFDoc.SaveOptions.e_linearized);
            }

            return true;
        }
            catch(Exception ex)
            {
                DMELoggerExtension.LogInformation(string.Format("Error GenerateImageContactSheetProof - {0}", ex.Message), DMELogType.Exception, DMELogPriority.High, request.TaskWk);
                throw;
            }
        }

        public bool GenerateContactSheetV2(GenerateContactSheetV2Request request)
        {
            try
            {
            // Log what we're doing...
            DMELoggerExtension.LogInformation(string.Format("Begin GenerateContactSheetV2 - {0}", JsonConvert.SerializeObject(request)), DMELogType.Informtation, DMELogPriority.High, request.TaskWk);

            // Offset to move top coupons down for the header
            const int verticalOffset = 90;
            //Replacement strings
            const string topLeft = "Top Left";
            const string topCenter = "Top Center";
            const string topRight = "Top Rightttttttttttttt";
            const string bottomLeft = "Bottom Left";
            const string bottomCenter = "Bottom Center";
            const string bottomRight = "Bottom Rightttttttttttttt";

            PDFNet.Initialize(_config.PDFTronKey);

            var proofResult = request.ProofDetails;

            var themeFiles = proofResult.Select(x => x.FileName).ToList();
            // Information for each coupon's proof header
            var couponDetails = request.CouponDetails;
            var couponInformation = couponDetails.ToDictionary(c => c.coupon_wk);

            DMELoggerExtension.LogInformation($"Got proof result [JobWk: {request.JobWk}][Files: {themeFiles.Count}]...", DMELogType.Informtation, DMELogPriority.High, request.TaskWk);

            var out_doc = new PDF.PDFDoc();

            var pageCount = 0;

            // Decodes a string of this format  \\svrrrint03\BDC_2007\shipping\push\variable_print\521082_60767_FDICON.pdf
            // where 521082 is the requestWk, 60767 is the couponWk, and FDICON is the couponTheme.
            var fileNameDecoder = new Regex(@"\\\d+_(\d+)_(.+)\.pdf");

            foreach (var themeFile in themeFiles)
            {
                var fileNameMatches = fileNameDecoder.Match(themeFile);

                var couponWk = System.Convert.ToInt32(fileNameMatches.Groups[1].Value);
                var couponTheme = fileNameMatches.Groups[2].Value;
                var couponInfo = couponInformation[couponWk];

                DMELoggerExtension.LogInformation(
                    $"Got header data. {nameof(couponInfo.coupon_code)}: '{couponInfo.coupon_code}'; " +
                     $"{nameof(couponInfo.coupon_name)}: '{couponInfo.coupon_name}'; " +
                     $"{nameof(couponInfo.campaign_type)}: '{couponInfo.campaign_type}'; " +
                $"{nameof(couponInfo.usage)}: {couponInfo.usage}; " +
                     $"{nameof(couponInfo.coupon_type)}: '{couponInfo.coupon_type}'", DMELogType.Debug, DMELogPriority.High, request.TaskWk);
                DMELoggerExtension.LogInformation($"Processing file [JobWk: {request.JobWk}][File: {themeFile}]...", DMELogType.Informtation, DMELogPriority.High, request.TaskWk);

                using (var in_doc = new PDF.PDFDoc(themeFile))
                using (var headerDoc = new PDF.PDFDoc(GetContactSheetV2HeaderFileStream()))
                {
                    DMELoggerExtension.LogInformation($"Getting PDF {themeFile}", DMELogType.Informtation, DMELogPriority.High, request.TaskWk);
                    in_doc.InitSecurityHandler();

                    // Create a list of pages to import from one PDF document to another.
                    var import_list = new ArrayList();
                    for (var itr = in_doc.GetPageIterator(); itr.HasNext(); itr.Next())
                        import_list.Add(itr.Current());

                    using (var new_doc = new PDF.PDFDoc()) //  Create a new document
                    using (var builder = new PDF.ElementBuilder())
                    using (var writer = new PDF.ElementWriter())
                    {
                        var headerPage = headerDoc.GetPage(1);
                        var imported_pages = new_doc.ImportPages(import_list);

                        // Paper dimension for A4 format in points.
                        // 8.27 × 11.69 inches. Its dimensions are rounded off to 595 × 842 points
                        var media_box = new PDF.Rect(0, 0, 842, 595);
                        var horizontal_mid_point = media_box.Width() / 2;
                        var vertical_mid_point = (media_box.Height()) / 2;

                        for (var i = 0; i < imported_pages.Count; ++i)
                        {
                            // Create a blank new A4 page and place on it three pages from the input document.
                            var new_page = new_doc.PageCreate(media_box);
                            writer.Begin(new_page);

                            using (var replacer = new PDF.ContentReplacer())
                            {
                                if (couponInfo.coupon_name.Length > 49)
                                    couponInfo.coupon_name =
                                        $"{couponInfo.coupon_name.Substring(0, 49)}...";
                                replacer.AddString(topLeft, $"{couponInfo.coupon_code} - {couponInfo.coupon_name}");
                                replacer.AddString(topCenter, $"{couponInfo.campaign_type}     Usage: {couponInfo.usage}");
                                replacer.AddString(topRight, couponTheme);
                                replacer.AddString(bottomLeft, $"{couponInfo.coupon_type}");
                                replacer.AddString(bottomCenter, "");
                                replacer.AddString(bottomRight, "");

                                replacer.Process(headerPage);
                            }
                            var newPages = new_doc.ImportPages(new ArrayList { headerPage });
                            headerPage = (PDF.Page)newPages[0];
                            var element = builder.CreateForm(headerPage);
                            element.GetGState().SetTransform(1, 0, 0, 1, 0, 595 - 55);
                            writer.WritePlacedElement(element);

                            // Place the first page
                            var src_page = (PDF.Page)imported_pages[i];
                            var sc_x = horizontal_mid_point / src_page.GetPageWidth();
                            var sc_y = vertical_mid_point / src_page.GetPageHeight();
                            var scale = Math.Min(sc_x, sc_y);
                            element = builder.CreateForm(src_page);
                            element.GetGState().SetTransform(scale, 0, 0, scale, 10, vertical_mid_point - verticalOffset);
                            writer.WritePlacedElement(element);

                            DMELoggerExtension.LogInformation($"Processed first page [JobWk: {request.JobWk}][File: {themeFile}]...", DMELogType.Informtation, DMELogPriority.High, request.TaskWk);

                            // Place the second page
                            ++i;
                            if (i < imported_pages.Count)
                            {
                                src_page = (PDF.Page)imported_pages[i];
                                element = builder.CreateForm(src_page);
                                sc_x = horizontal_mid_point / src_page.GetPageWidth();
                                sc_y = vertical_mid_point / src_page.GetPageHeight();
                                scale = Math.Min(sc_x, sc_y);
                                element.GetGState().SetTransform(scale, 0, 0, scale, (horizontal_mid_point - 100), vertical_mid_point - verticalOffset);
                                writer.WritePlacedElement(element);

                                DMELoggerExtension.LogInformation($"Processed second page [JobWk: {request.JobWk}][File: {themeFile}]...", DMELogType.Informtation, DMELogPriority.High, request.TaskWk);
                            }

                            // Place the third page
                            ++i;
                            if (i < imported_pages.Count)
                            {
                                src_page = (PDF.Page)imported_pages[i];
                                element = builder.CreateForm(src_page);

                                scale = 1;
                                element.GetGState().SetTransform(scale, 0, 0, scale, 10, 0);
                                writer.WritePlacedElement(element);

                                DMELoggerExtension.LogInformation($"Processed third page [JobWk: {request.JobWk}][File: {themeFile}]...", DMELogType.Informtation, DMELogPriority.High, request.TaskWk);

                            }

                            writer.End();
                            new_doc.PagePushBack(new_page);

                            out_doc.InsertPages
                                (
                                pageCount == 0 ? 0 : pageCount + 1,
                                new_doc,
                                1,
                                new_doc.GetPageCount(),
                                // ...flags
                                PDF.PDFDoc.InsertFlag.e_none
                            );

                            DMELoggerExtension.LogInformation($"Processed file [JobWk: {request.JobWk}][File: {themeFile}][Merged Page Count: {out_doc.GetPageCount()}]...", DMELogType.Informtation, DMELogPriority.High, request.TaskWk);

                        }
                    }
                }

                pageCount = out_doc.GetPageCount();
            }

            var destLocation = _config.OnDemandFileStore_PDFO;
            var outputFile = $@"{destLocation}\{request.JobWk}_coupon_contact_sheet.pdf";

            out_doc.Save(outputFile, pdftron.SDF.SDFDoc.SaveOptions.e_linearized);
            var msg =
                $"Saved final output file [JobWk: {request.JobWk}]" +
                $"[File: {outputFile}]" +
                $"[Page Count: {out_doc.GetPageCount()}]...";
            DMELoggerExtension.LogInformation(msg, DMELogType.Informtation, DMELogPriority.High, request.TaskWk);


            return true;
        }
            catch (Exception ex)
            {
                DMELoggerExtension.LogInformation(string.Format("Error GenerateContactSheetV2 - {0}", ex.Message), DMELogType.Exception, DMELogPriority.High, request.TaskWk);
                throw;
            }
        }

        public bool ProcessRotateProofPages(RotatePagesRequest request)
        {
            try
            {
            RotateProofPages(request.WorkItem, request.FileName);
            return true;
        }
            catch (Exception ex)
            {
                DMELoggerExtension.LogInformation(string.Format("Error ProcessRotateProofPages - {0}", ex.Message), DMELogType.Exception, DMELogPriority.High, request.WorkItem.CifTaskWk);
                throw;
            }
        }

        private void RotateProofPages(XmpieJobWorkItem workitem, string fileName)
        {
            //  initalize the license key
            DMELoggerExtension.LogInformation(string.Format("Begin RotateProofPages: Job_Wk {0}, Task_Wk {1}, Task Name {2}", workitem.VdfJobWk, workitem.CifTaskWk, workitem.ProofTaskDefnName), DMELogType.Informtation, DMELogPriority.High, workitem.CifTaskWk);

            PDFNet.Initialize(_config.PDFTronKey);
            DMELoggerExtension.LogInformation(string.Format("PDFTron initialized: Key {0}", "***"), DMELogType.Informtation, DMELogPriority.High, workitem.CifTaskWk);
            if (workitem.RotatePages != XmpieRotatePages.None)
            {
                //  get rotation (90, 180, 270)
                pdftron.PDF.Page.Rotate rotation = this.TransateRotation(workitem.Rotation);

                using (pdftron.PDF.PDFDoc doc = new pdftron.PDF.PDFDoc(fileName))
                {
                    doc.InitSecurityHandler();
                    int page_num = doc.GetPageCount();
                    DMELoggerExtension.LogInformation(string.Format("Proof Page Count: {0}", page_num), DMELogType.Informtation, DMELogPriority.High, workitem.CifTaskWk);
                    DMELoggerExtension.LogInformation(string.Format("Proof Rotate Pages: {0}", workitem.RotatePages.GetName<XmpieRotatePages>()), DMELogType.Informtation, DMELogPriority.High, workitem.CifTaskWk);
                    DMELoggerExtension.LogInformation(string.Format("Proof Page Rotation: {0}", workitem.Rotation), DMELogType.Informtation, DMELogPriority.High, workitem.CifTaskWk);

                    for (int i = 1; i <= page_num; i++)
                    {
                        //  odd or even page?
                        bool rotate = (i % 2 == (workitem.RotatePages == XmpieRotatePages.Even ? 0 : 1));
                        if (rotate)
                        {
                            doc.GetPage(i).SetRotation(rotation);
                        }
                    }
                    //  save the changes
                    doc.Save(fileName, pdftron.SDF.SDFDoc.SaveOptions.e_compatibility);
                    DMELoggerExtension.LogInformation(string.Format("End RotateProofPages: Saved File Name {0}", fileName), DMELogType.Informtation, DMELogPriority.High, workitem.CifTaskWk);
                }
            }
        }

        private void CorrectImageOrientationCoupon(string[] sourcePaths, string[] destinationPaths, XmpieJobWorkItem workitem)
        {
            //  flip pages if configured to do so
            //  supported scenarios: 
            //      PDF Proof from ODP 
            //      PDF Proof from Split Job
            //      Sample Print with PDFO output where the split has a rotate pages configuration set
            if (((workitem.RequestType == XmpieRequestType.Proof)
                 || (workitem.RequestType == XmpieRequestType.SamplePrint && workitem.OutPutType == XmpieOutputType.PDFO))
                && workitem.RotatePages != XmpieRotatePages.None)
            {
                foreach (var destPath in destinationPaths)
                {
                    foreach (var fileName in sourcePaths)
                    {
                        var name = Path.GetFileName(fileName);
                        var destFile = Path.Combine(destPath, name);
                        RotateProofPages(workitem, destFile);
                    }
                }
            }
        }

        private void CorrectImageOrientation(List<string> sourcePaths, string[] destinationPaths, XmpieJobWorkItem workitem)
        {
            //  flip pages if configured to do so
            //  supported scenarios: 
            //      PDF Proof from ODP 
            //      PDF Proof from Split Job
            //      Sample Print with PDFO output where the split has a rotate pages configuration set
            if (((workitem.RequestType == XmpieRequestType.Proof)
                || (workitem.RequestType == XmpieRequestType.SamplePrint && workitem.OutPutType == XmpieOutputType.PDFO))
                    && workitem.RotatePages != XmpieRotatePages.None)
            {
                foreach (var destPath in destinationPaths)
                {
                    foreach (var fileName in sourcePaths)
                    {
                        var name = fileName;
                        var destFile = Path.Combine(destPath, name);
                        RotateProofPages(workitem, destFile);
                    }
                }
            }
        }


        /// <summary>
		/// Remove Original PDF Files after merge
		/// </summary>
		/// <param name="inputFiles">List of files</param>
		/// <param name="splitTaskWk">TaskWk for messaging</param>
		private void RemoveOriginalPDFFiles(string[] inputFiles, int? splitTaskWk, string msg)
        {
            try
            {
                // Log what we're doing...
                DMELoggerExtension.LogInformation
                    (
                        string.Format(msg, inputFiles.Length),
                        DMELogType.Informtation,
                        DMELogPriority.High,
                        splitTaskWk
                    );

                foreach (string inputFile in inputFiles)
                {
                    // Log what we're doing...
                    DMELoggerExtension.LogInformation
                            (
                                string.Format("Deleting file: {0}.", inputFile),
                                DMELogType.Informtation,
                                DMELogPriority.High,
                                splitTaskWk
                            );

                    // Delete the file...
                    if (File.Exists(inputFile))
                        File.Delete(inputFile);
                }

                // Log what we're doing...
                DMELoggerExtension.LogInformation
                    (
                        string.Format("{0} Original files deleted.", inputFiles.Length),
                        DMELogType.Informtation,
                        DMELogPriority.High,
                        splitTaskWk
                    );
            }
            catch (Exception ex)
            {
                // Log failure
                DMELoggerExtension.LogException
                    (
                        ex,
                        DMELogType.Exception,
                        DMELogPriority.High,
                        splitTaskWk
                    );

                // rethrow the exception...
                throw;
            }
        }

        private bool DeleteOriginalPDFFiles(int vdfJobWk, string[] inputFiles, int? cifTaskWk, string msg)
        {
            bool deleteSuccess = false;

            try
            {
                RemoveOriginalPDFFiles(inputFiles, cifTaskWk, msg);
                DeleteMergeFiles(vdfJobWk);
                deleteSuccess = true;
            }
            catch (Exception ex)
            {
                DMELoggerExtension.LogInformation
                 (
                     $"Failed deleting original files. {ex.Message}",
                     DMELogType.Informtation,
                     DMELogPriority.High,
                     cifTaskWk
                 );
            }

            return deleteSuccess;
        }

        /// <summary>
		/// Purpose: Saves merged PDF documents.
		/// </summary>
		/// <param name="merged_doc">PDFDoc</param>
		/// <param name="inputFile">Input filename</param>
		/// <param name="outputFile">Name of saved file.</param>
		/// <param name="splitTaskWk">TaskWk for messaging</param>
		private void SavePDF(PDF.PDFDoc merged_doc, 
            string inputFile, 
            string outputFile, 
            int? splitTaskWk, 
            bool optimize = false, 
            CancellationToken cancellationToken = default(CancellationToken),
            bool isCloseDoc = true)
        {
            string outputFileName = string.Empty;
            int maxRetries = 4;
            int retryCount = 1;
            while (retryCount <= maxRetries)
            {
                try
                {
                    outputFileName = outputFile.Substring(outputFile.LastIndexOf("\\") + 1);

                    // throw error is cancellation requested
                    cancellationToken.ThrowIfCancellationRequested();

                    if (_config.VerboseLogging)
                    {
                        DMELoggerExtension.LogInformation
                            (
                                string.Format("BEGIN: SavePDF() for {0}. Retry: {1}", outputFile, retryCount),
                                DMELogType.Informtation,
                                DMELogPriority.High,
                                splitTaskWk
                            );
                    }

                    // Optimize our merged file...
                    if (optimize)
                    {
                        if (_config.VerboseLogging)
                            DMELoggerExtension.LogInformation
                            (
                                string.Format("BEGIN: File optimization: {0}. Retry: {1}", outputFileName, retryCount),
                                DMELogType.Informtation,
                                DMELogPriority.High,
                                splitTaskWk
                            );

                        // Set Optimizer settings...
                        PDF.Optimizer.ImageSettings image_settings = new PDF.Optimizer.ImageSettings();
                        image_settings.SetCompressionMode(PDF.Optimizer.ImageSettings.CompressionMode.e_jpeg2000);
                        // Set 300 as default DPI.
                        image_settings.SetImageDPI(300, 150);
                        image_settings.ForceRecompression(true);

                        PDF.Optimizer.OptimizerSettings opt_settings = new PDF.Optimizer.OptimizerSettings();
                        opt_settings.SetColorImageSettings(image_settings);
                        opt_settings.SetGrayscaleImageSettings(image_settings);


                        // Optimize file...
                        PDF.Optimizer.Optimize(merged_doc, opt_settings);

                        if (_config.VerboseLogging)
                            DMELoggerExtension.LogInformation
                                (
                                    string.Format("END: File optimization: {0}. Retry: {1}", outputFileName, retryCount),
                                    DMELogType.Informtation,
                                    DMELogPriority.High,
                                    splitTaskWk
                                );
                    }

                    // Save our merged file...
                    DMELoggerExtension.LogInformation
                        (
                            string.Format("Saving file: {0}. File has {1} pages. {2}", outputFile, merged_doc.GetPageCount(), outputFileName),
                            DMELogType.Informtation,
                            DMELogPriority.High,
                            splitTaskWk
                        );

                    merged_doc.Save(outputFile, pdftron.SDF.SDFDoc.SaveOptions.e_compatibility);
                    if (isCloseDoc)
                    {
                        merged_doc.Close();
                        merged_doc.Dispose();
                    }

                    DMELoggerExtension.LogInformation
                        (
                            string.Format("END: SavePDF() for {0}. Retry: {1}", outputFile, retryCount),
                            DMELogType.Informtation,
                            DMELogPriority.High,
                            splitTaskWk
                        );

                    // File optimization successful...
                    break;

                }
                catch (Exception ex)
                {
                    var vx = new VdfException(string.Format("Encountered error during File optimization: {0}. Retry: {1}", outputFileName, retryCount), ex);

                    // Log failure
                    DMELoggerExtension.LogException
                        (
                            vx,
                            DMELogType.Exception,
                            DMELogPriority.High,
                            splitTaskWk
                        );

                    // Increment our retry counter...
                    retryCount++;
                    
                    if (retryCount <= maxRetries)
                    {
                        DMELoggerExtension.LogInformation(string.Format("Sleeping for 5 seconds before processing {0}. Retry: {1}...", outputFileName, retryCount), DMELogType.Informtation, DMELogPriority.High, splitTaskWk);
                        
                        // Sleep for 5 seconds...
                        Thread.Sleep(5000);

                        // Wake up and try again...
                        DMELoggerExtension.LogInformation(string.Format("Awake, time to try again {0}. Retry: {1}...", outputFileName, retryCount), DMELogType.Informtation, DMELogPriority.High, splitTaskWk);
                    }
                    else
                    {
                        // rethrow the exception...
                        throw;
                    }
                }
            }
            
        }

        private pdftron.PDF.Page.Rotate TransateRotation(int rotation)
        {
            var rotate = pdftron.PDF.Page.Rotate.e_0;
            switch (rotation)
            {
                case 90:
                    rotate = pdftron.PDF.Page.Rotate.e_90;
                    break;

                case 180:
                    rotate = pdftron.PDF.Page.Rotate.e_180;
                    break;

                case 270:
                    rotate = pdftron.PDF.Page.Rotate.e_270;
                    break;
            }

            return rotate;
        }

        public FileStream GetContactSheetV2HeaderFileStream()
        {
            var headerFilePath = _config.ContactSheetHeaderFilePath;
            return File.OpenRead(headerFilePath);
        }

    }
}
