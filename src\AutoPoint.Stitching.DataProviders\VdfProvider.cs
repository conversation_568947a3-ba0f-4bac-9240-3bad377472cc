﻿using AutoPoint.Stitching.Common;
using AutoPoint.Stitching.Common.Interfaces;
using AutoPoint.Stitching.Common.Logger;
using AutoPoint.Stitching.Common.Models;
using AutoPoint.Stitching.Contracts.Enums;
using Microsoft.Extensions.Configuration;
using System.Data.SqlClient;
using Dapper;
using Serilog;
using System.Data;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System;

namespace AutoPoint.Stitching.DataProviders
{
    public class VdfProvider : IVdfProvider
    {
        private readonly Serilog.ILogger _logger;
        private readonly IConfiguration _config;
        private readonly IRetryPolicy _retryPolicy;

        public VdfProvider(IConfiguration config, ILogger logger, IRetryPolicy retryPolicy)
        {
            _config = config;
            _logger = logger;
            _retryPolicy = retryPolicy;
        }
        public async Task<MailstreamStitchConfig> GetOnDemandMailStreamStitchConfig(int jobWk, string outputType)
        {
            var response = new List<MailstreamStitchConfig> { };

            try
            {
                using (var connection = new SqlConnection(_config["ConnectionStrings:RRS_VDF"]))
                {
                    var responseDto = (await connection.OpenWithRetryAsync(cn => cn.QueryAsync<MailstreamStitchConfigDto>("marketnow.MailFileStitch_Config_Get",
                        new { @vdf_job_wk = jobWk, @output_type = outputType },
                        commandType: CommandType.StoredProcedure),
                        _retryPolicy)).ToList();

                    response = responseDto.Select(item => new MailstreamStitchConfig()
                    {
                        SpoolSize = item.spool_size,
                        CatalogOrderWk = item.order_number,
                        OrderQuantity = item.order_quantity
                    }).ToList();

                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"GetOnDemandMailStreamStitchConfig failed with {ex.Message}");
                DMELoggerExtension.LogInformation(ex.Message, DMELogType.Exception, DMELogPriority.High);
                throw;
            }

            return response.FirstOrDefault();
        }

        public async Task<IList<Mailstream2Filename>> GetMailstreamFilenames(int jobWk, int batchSize, string outputFolder, bool commingleOnly, string fileAppend = "")
        {
            var response = new List<Mailstream2Filename> { };

            try
            {
                using (var connection = new SqlConnection(_config["ConnectionStrings:RRS_VDF"]))
                {
                    var responseDto = (await connection.OpenWithRetryAsync(cn => cn.QueryAsync<Mailstream2FilenameDto>("marketnow.mailstream2filename",
                        new { @job_wk = jobWk, @batch_size = batchSize, @folder_name = outputFolder, @commingle_only = commingleOnly, @file_append = fileAppend },
                        commandType: CommandType.StoredProcedure),
                        _retryPolicy)).ToList();

                    response = responseDto.Select(item => new Mailstream2Filename()
                    {
                        rowNumber = item.row_no,
                        batchNumber = item.batch_no,
                        cifWk = item.cif_wk,
                        presortId = item.presort_id,
                        filename = item.xmpie_filename
                    }).ToList();

                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"GetMailstreamFilenames failed with {ex.Message}");
                DMELoggerExtension.LogInformation(ex.Message, DMELogType.Exception, DMELogPriority.High);
                throw;
            }

            return response;
        }

        public async Task<IList<ODPCommunicationOrder>> GetODPFilesInCommOrder(int jobWk, string marketingStrategyName, int numPages)
        {
            var response = new List<ODPCommunicationOrder> { };

            try
            {
                using (var connection = new SqlConnection(_config["ConnectionStrings:RRS_VDF"]))
                {
                    var responseDto = (await connection.OpenWithRetryAsync(cn => cn.QueryAsync<ODPCommunicationOrderDto>("work.proof_request_communications_odp_get",
                        new { @job_wk = jobWk, @marketing_strategy_name = marketingStrategyName, @num_pages = numPages },
                        commandType: CommandType.StoredProcedure),
                    _retryPolicy)).ToList();

                    response = responseDto.Select(item => new ODPCommunicationOrder()
                    {
                        startPage = item.start_page.HasValue ? (int)item.start_page : 0,
                        Filename = item.proofing_communication_filename
                    }).ToList();

                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"GetODPFilesInCommOrder failed with {ex.Message}");
                DMELoggerExtension.LogInformation(ex.Message, DMELogType.Exception, DMELogPriority.High);
                throw;
            }

            return response;
        }

        public async Task<SortGroup> GetSortGroup(int vdfJobWk)
        {
            var response = new SortGroup { };

            try
            {
                using (var connection = new SqlConnection(_config["ConnectionStrings:RRS_VDF"]))
                {
                    var responseDto = (await connection.OpenWithRetryAsync(cn => cn.QueryAsync<SortGroupDto>("marketnow.SortGroup_Get",
                        new { @vdf_job_wk = vdfJobWk },
                        commandType: CommandType.StoredProcedure),
                    _retryPolicy)).ToList();

                    response = responseDto.Select(item => new SortGroup()
                    {
                        AuditWk = item.audit_wk ?? -1,
                        SortGroupDefnWk = item.sort_group_defn_wk,
                        SortGroupWk = item.sort_group_wk,
                        SortGroupName = item.sort_group_name,
                        DueDate = item.due_date,
                        EndDate = item.end_date,
                        EndRecordCount = item.end_record_count ?? 0,
                        ErrorRecordCount = item.error_record_count ?? 0,
                        Mailing = item.mailing,
                        ReferenceId = item.reference_id,
                        SourceJobWk = item.source_job_wk ?? -1,
                        SplitName = item.name,
                        StartDate = item.start_date,
                        StartRecordCount = item.start_record_count,
                        VDFJobWk = item.vdf_job_wk ?? -1,
                        IsMixedWeightPresort = false,
                        IsSortOverride = false,
                        IncludeCouponContactSheet = false,
                        RotateProofPages = 0,
                        Rotation = 0,
                        IsManifestMailing = false,
                        IsTestMode = item.is_test_mode,
                        IncludeInkJet = item.include_inkjet,
                        IncludeStoreFrontDetails = item.include_storefront_details,
                        IsIPN = item.is_ipn ?? false
                    }).FirstOrDefault();

                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"GetODPFilesInCommOrder failed with {ex.Message}");
                DMELoggerExtension.LogInformation(ex.Message, DMELogType.Exception, DMELogPriority.High);
                throw;
            }

            return response;
        }

        public async Task<IList<MarketNowDIFProofXmpie>> GetOnDemandDIFProofXmpieJobs(int jobWk)
        {
            var response = new List<MarketNowDIFProofXmpie> { };

            try
            {
                using (var connection = new SqlConnection(_config["ConnectionStrings:RRS_VDF"]))
                {
                    var responseDto = (await connection.OpenWithRetryAsync(cn => cn.QueryAsync<MarketNowDIFProofXmpieDto>("marketnow.dif_proof_xmpie_job_get",
                        new { @job_wk = jobWk },
                        commandType: CommandType.StoredProcedure),
                    _retryPolicy)).ToList();

                    response = responseDto.Select(item => new MarketNowDIFProofXmpie()
                    {
                        CatalogOrderWk = item.catalog_order_wk ?? -1,
                        OrgWk = item.org_wk ?? -1,
                        DocumentID = item.document_id ?? -1,
                        XmpieJobID = item.xmpie_job_id ?? -1
                    }).ToList();

                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"GetOnDemandDIFProofXmpieJobs failed with {ex.Message}");
                DMELoggerExtension.LogInformation(ex.Message, DMELogType.Exception, DMELogPriority.High);
                throw;
            }

            return response;
        }

        public async Task<bool> UpdateTaskStatusTracking(int taskTrackingRequestWk, string statusInd, string statusDesc)
        {
            try
            {
                using (var connection = new SqlConnection(_config["ConnectionStrings:RRS_VDF"]))
                {
                    await connection.OpenWithRetryAsync(cn => cn.ExecuteAsync("queue.task_abort_request_update_v2",
                      new
                      {
                          @task_abort_request_wk = taskTrackingRequestWk,
                          @status_ind = statusInd,
                          @status_desc = statusDesc
                      }, commandType: CommandType.StoredProcedure), _retryPolicy);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"UpdateTaskStatusTracking failed with {ex.Message}");
                DMELoggerExtension.LogInformation(ex.Message, DMELogType.Exception, DMELogPriority.High);
                return false;
            }
        }

        public async Task<bool> OnDemandMergeFileMailStreamAddRequests(int jobWk, int taskWk, bool isCommingle, string requestJson)
        {
            try
            {
                using (var connection = new SqlConnection(_config["ConnectionStrings:RRS_VDF"]))
                {
                    await connection.OpenWithRetryAsync(cn => cn.ExecuteAsync("work.mailstream2filename_add ",
                      new
                      {
                          @job_wk = jobWk,
                          @is_commingle = isCommingle,
                          @request_json = requestJson
                      }, commandType: CommandType.StoredProcedure), _retryPolicy);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"OnDemandMergeFileMailStreamAddRequests failed with {ex.Message}");
                DMELoggerExtension.LogInformation(ex.Message, DMELogType.Exception, DMELogPriority.High, taskWk);
                throw;
            }
        }

        public async Task<bool> OnDemandMergeFileMailStreamDeleteRequests(int jobWk, int taskWk, bool isCommingle)
        {
            try
            {
                using (var connection = new SqlConnection(_config["ConnectionStrings:RRS_VDF"]))
                {
                    await connection.OpenWithRetryAsync(cn => cn.ExecuteAsync("work.mailstream2filename_delete ",
                      new
                      {
                          @job_wk = jobWk,
                          @is_commingle = isCommingle
                      }, commandType: CommandType.StoredProcedure), _retryPolicy);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"OnDemandMergeFileMailStreamDeleteRequests failed with {ex.Message}");
                DMELoggerExtension.LogInformation(ex.Message, DMELogType.Exception, DMELogPriority.High, taskWk);
                throw;
            }
        }

        public async Task<bool> OnDemandMergeFileMailStreamUpdateStatus(int jobWk, int taskWk, bool isCommingle, int fileIndex, string statusIndicator, string msg, int retries)
        {
            try
            {
                using (var connection = new SqlConnection(_config["ConnectionStrings:RRS_VDF"]))
                {
                    await connection.OpenWithRetryAsync(cn => cn.ExecuteAsync("work.mailstream2filename_update_status ",
                      new
                      {
                          @job_wk = jobWk,
                          @is_commingle = isCommingle,
                          @file_index = fileIndex,
                          @status_indicator = statusIndicator,
                          @msg = msg,
                          @retries = retries
                      }, commandType: CommandType.StoredProcedure), _retryPolicy);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"OnDemandMergeFileMailStreamUpdateStatus failed with {ex.Message}");
                DMELoggerExtension.LogInformation(ex.Message, DMELogType.Exception, DMELogPriority.High, taskWk);
                throw;
            }
        }

        public async Task<IList<Mailstream2FilenameResponse>> GetOnDemandMergeFileMailStreamRequests(int jobWk, int taskWk, bool isCommingle)
        {
            var response = new List<Mailstream2FilenameResponse> { };

            try
            {
                using (var connection = new SqlConnection(_config["ConnectionStrings:RRS_VDF"]))
                {
                    var responseDto = (await connection.OpenWithRetryAsync(cn => cn.QueryAsync<Mailstream2FilenameResponseDto>("work.mailstream2filename_get_list",
                        new { @job_wk = jobWk, @is_commingle = isCommingle },
                        commandType: CommandType.StoredProcedure),
                    _retryPolicy)).ToList();

                    response = responseDto.Select(item => new Mailstream2FilenameResponse()
                    {
                        JobWk = item.job_wk,  
                        FileIndex = item.file_index,
                        IsCommingle = item.is_commingle,
                        MinPresortId = item.min_presort_id,
                        MaxPresortId = item.max_presort_id,
                        StatusIndicator = item.status_indicator,
                        Message = item.msg,
                        Retries = item.retries
                    }).ToList();

                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"GetOnDemandMergeFileMailStreamRequests failed with {ex.Message}");
                DMELoggerExtension.LogInformation(ex.Message, DMELogType.Exception, DMELogPriority.High, taskWk);
                throw;
            }

            return response;
        }

    }
}
