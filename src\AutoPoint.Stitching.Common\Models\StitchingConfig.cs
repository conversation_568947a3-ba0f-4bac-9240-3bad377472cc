﻿using AutoPoint.Stitching.Common.Interfaces;
using Microsoft.Extensions.Configuration;
using System;

namespace AutoPoint.Stitching.Common.Models
{
    public class StitchingConfig : IStitchingConfig
    {
        public StitchingConfig(IConfiguration configuration)
        {
            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));

            configuration.Bind("StitchingSettings", this);
        }
#pragma warning restore IDE0079 // Remove unnecessary suppression

        public int DefaultBatchSize { get; set; }
        public int DefaultPagesPerContact { get; set; }
        public bool OnDemandStitchDeleteOriginals { get; set; }
        public string OnDemandFileStore_PDFO { get; set; }
        public string OnDemandFileStore_PDFVT { get; set; }
        public string OnDemandFileStore_CampaignProofs { get; set; }
        public int MergeFileContacts { get; set; }
        public int MergeFileSaveThreads { get; set; }
        public string OnDemandFile_OutPutType { get; set; }
        public string OnDemandFileStore_Local { get; set; }
        public int OnDemandStitchDiskSpaceInMB { get; set; }
        public int OnDemandStitchCPUThresholdPercent { get; set; }
        public int OnDemandStitchRAMThresholdPercent { get; set; }
        public int OnDemandStitchJobsThreshold { get; set; }
        public int SingeOrderCIFStitchingThreshold { get; set; }
        public string MergedCompleteFilename { get; set; }
        public bool NewRelicAgentEnabled { get; set; }
        public string NewRelicAppName { get; set; }
        public string NewRelicLicenseKey { get; set; }
        public string PDFTronKey { get; set; }
        public string ContactSheetHeaderFilePath { get; set; }
        public bool VerboseLogging { get; set; }
        public string OnDemandFileStore_UNC { get; set; }
    }
}
